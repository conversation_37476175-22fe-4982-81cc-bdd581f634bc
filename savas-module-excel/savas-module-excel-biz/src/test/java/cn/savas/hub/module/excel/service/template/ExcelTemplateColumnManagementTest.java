package cn.savas.hub.module.excel.service.template;

import cn.savas.hub.module.excel.controller.admin.template.vo.*;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel 模板列信息管理功能测试
 * <AUTHOR>
 * @date 2025/9/2
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ExcelTemplateColumnManagementTest {

    @Resource
    private ExcelTemplateService excelTemplateService;

    /**
     * 测试创建模板列信息
     */
    @Test
    public void testCreateTemplateColumn() {
        // 1. 先创建一个测试模板
        Long templateId = createTestTemplate();
        
        // 2. 创建列信息
        ExcelColumnCreateReqVO createReqVO = new ExcelColumnCreateReqVO();
        createReqVO.setTemplateId(templateId);
        createReqVO.setColumnName("商品名称");
        createReqVO.setColumnField("productName");
        createReqVO.setColumnFieldType("String");
        createReqVO.setColumnIndex(0);
        createReqVO.setRowIndex(0);
        createReqVO.setColumnSpan(1);
        createReqVO.setRowSpan(1);
        createReqVO.setColumnDefaultValue("");
        createReqVO.setColumnHidden(false);

        Long columnId = excelTemplateService.createTemplateColumn(createReqVO);
        
        // 3. 验证结果
        assertNotNull(columnId);
        assertTrue(columnId > 0);
        
        // 4. 查询验证
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columns = 
                excelTemplateService.getTemplateColumns(templateId);
        assertEquals(1, columns.size());
        assertEquals("商品名称", columns.get(0).getColumnName());
        assertEquals("productName", columns.get(0).getColumnField());
    }

    /**
     * 测试更新模板列信息
     */
    @Test
    public void testUpdateTemplateColumn() {
        // 1. 创建测试数据
        Long templateId = createTestTemplate();
        Long columnId = createTestColumn(templateId);
        
        // 2. 更新列信息
        ExcelColumnUpdateReqVO updateReqVO = new ExcelColumnUpdateReqVO();
        updateReqVO.setColumnId(columnId);
        updateReqVO.setColumnName("商品价格");
        updateReqVO.setColumnField("productPrice");
        updateReqVO.setColumnFieldType("Double");
        updateReqVO.setColumnIndex(1);
        updateReqVO.setRowIndex(0);
        updateReqVO.setColumnSpan(1);
        updateReqVO.setRowSpan(1);
        updateReqVO.setColumnDefaultValue("0.00");
        updateReqVO.setColumnHidden(false);

        excelTemplateService.updateTemplateColumn(updateReqVO);
        
        // 3. 验证结果
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columns = 
                excelTemplateService.getTemplateColumns(templateId);
        assertEquals(1, columns.size());
        assertEquals("商品价格", columns.get(0).getColumnName());
        assertEquals("productPrice", columns.get(0).getColumnField());
        assertEquals("Double", columns.get(0).getColumnFieldType());
    }

    /**
     * 测试删除模板列信息
     */
    @Test
    public void testDeleteTemplateColumn() {
        // 1. 创建测试数据
        Long templateId = createTestTemplate();
        Long columnId = createTestColumn(templateId);
        
        // 2. 删除列信息
        excelTemplateService.deleteTemplateColumn(columnId);
        
        // 3. 验证结果
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columns = 
                excelTemplateService.getTemplateColumns(templateId);
        assertEquals(0, columns.size());
    }

    /**
     * 测试批量保存模板列信息
     */
    @Test
    public void testBatchSaveTemplateColumns() {
        // 1. 创建测试模板
        Long templateId = createTestTemplate();
        
        // 2. 准备批量保存数据
        ExcelColumnBatchSaveReqVO batchSaveReqVO = new ExcelColumnBatchSaveReqVO();
        batchSaveReqVO.setTemplateId(templateId);
        
        ExcelColumnBatchSaveReqVO.ExcelColumnBatchItemVO item1 = 
                new ExcelColumnBatchSaveReqVO.ExcelColumnBatchItemVO();
        item1.setColumnName("商品名称");
        item1.setColumnField("productName");
        item1.setColumnFieldType("String");
        item1.setColumnIndex(0);
        item1.setRowIndex(0);
        item1.setColumnSpan(1);
        item1.setRowSpan(1);
        
        ExcelColumnBatchSaveReqVO.ExcelColumnBatchItemVO item2 = 
                new ExcelColumnBatchSaveReqVO.ExcelColumnBatchItemVO();
        item2.setColumnName("商品价格");
        item2.setColumnField("productPrice");
        item2.setColumnFieldType("Double");
        item2.setColumnIndex(1);
        item2.setRowIndex(0);
        item2.setColumnSpan(1);
        item2.setRowSpan(1);
        
        batchSaveReqVO.setColumns(Arrays.asList(item1, item2));
        
        // 3. 执行批量保存
        excelTemplateService.batchSaveTemplateColumns(batchSaveReqVO);
        
        // 4. 验证结果
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columns = 
                excelTemplateService.getTemplateColumns(templateId);
        assertEquals(2, columns.size());
        
        // 验证第一列
        ExcelTemplateDetailRespVO.ExcelColumnRespVO column1 = columns.stream()
                .filter(col -> col.getColumnIndex() == 0)
                .findFirst()
                .orElse(null);
        assertNotNull(column1);
        assertEquals("商品名称", column1.getColumnName());
        assertEquals("productName", column1.getColumnField());
        
        // 验证第二列
        ExcelTemplateDetailRespVO.ExcelColumnRespVO column2 = columns.stream()
                .filter(col -> col.getColumnIndex() == 1)
                .findFirst()
                .orElse(null);
        assertNotNull(column2);
        assertEquals("商品价格", column2.getColumnName());
        assertEquals("productPrice", column2.getColumnField());
    }

    /**
     * 测试获取模板列信息列表
     */
    @Test
    public void testGetTemplateColumns() {
        // 1. 创建测试数据
        Long templateId = createTestTemplate();
        createTestColumn(templateId);
        
        // 2. 查询列信息
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columns = 
                excelTemplateService.getTemplateColumns(templateId);
        
        // 3. 验证结果
        assertNotNull(columns);
        assertEquals(1, columns.size());
        assertEquals("测试列", columns.get(0).getColumnName());
    }

    /**
     * 创建测试模板
     */
    private Long createTestTemplate() {
        ExcelTemplateCreateReqVO createReqVO = new ExcelTemplateCreateReqVO();
        createReqVO.setTemplateName("测试模板_" + System.currentTimeMillis());
        createReqVO.setDescription("测试用模板");
        createReqVO.setTemplateType(1);
        createReqVO.setHeaderRows(1);
        createReqVO.setDataStartRow(2);
        createReqVO.setHeaderStructure("{}");
        
        try {
            return excelTemplateService.createTemplate(createReqVO);
        } catch (Exception e) {
            throw new RuntimeException("创建测试模板失败", e);
        }
    }

    /**
     * 创建测试列
     */
    private Long createTestColumn(Long templateId) {
        ExcelColumnCreateReqVO createReqVO = new ExcelColumnCreateReqVO();
        createReqVO.setTemplateId(templateId);
        createReqVO.setColumnName("测试列");
        createReqVO.setColumnField("testField");
        createReqVO.setColumnFieldType("String");
        createReqVO.setColumnIndex(0);
        createReqVO.setRowIndex(0);
        createReqVO.setColumnSpan(1);
        createReqVO.setRowSpan(1);
        createReqVO.setColumnDefaultValue("");
        createReqVO.setColumnHidden(false);

        return excelTemplateService.createTemplateColumn(createReqVO);
    }
}