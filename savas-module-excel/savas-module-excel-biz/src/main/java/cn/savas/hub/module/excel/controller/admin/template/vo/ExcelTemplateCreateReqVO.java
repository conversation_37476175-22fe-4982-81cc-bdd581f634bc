package cn.savas.hub.module.excel.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * Excel 模板创建请求 VO
 * <AUTHOR>
 * @date 2025/9/2
 */
@Schema(description = "管理后台 - Excel 动态模板创建请求 VO")
@Data
public class ExcelTemplateCreateReqVO {
    
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "销售数据模板")
    @NotBlank(message = "模板名称不能为空")
    private String templateName;
    
    @Schema(description = "模板描述", example = "用于导入销售数据的Excel模板")
    private String description;
    
    @Schema(description = "模板类型(1:标准价格库、2:电缆价格库)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "模板类型不能为空")
    private Integer templateType;
    
    @Schema(description = "表头占用的行数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "表头行数不能为空")
    @Min(value = 1, message = "表头行数最小为1")
    private Integer headerRows;
    
    @Schema(description = "数据起始行号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    @NotNull(message = "数据起始行号不能为空")
    @Min(value = 1, message = "数据起始行号最小为1")
    private Integer dataStartRow;
    
    @Schema(description = "表头结构（JSON格式，可选）", example = "{\"headers\": []}")
    private String headerStructure;
    
    @Schema(description = "模板文件", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板文件不能为空")
    private MultipartFile templateFile;
}
