package cn.savas.hub.module.excel.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Excel 列信息更新请求 VO
 * <AUTHOR>
 * @date 2025/9/2
 */
@Schema(description = "管理后台 - Excel 列信息更新请求 VO")
@Data
public class ExcelColumnUpdateReqVO {
    
    @Schema(description = "列唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "列ID不能为空")
    private Long columnId;
    
    @Schema(description = "Excel 中的原始列头名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "商品名称")
    @NotBlank(message = "列名称不能为空")
    private String columnName;
    
    @Schema(description = "映射字段名", requiredMode = Schema.RequiredMode.REQUIRED, example = "productName")
    @NotBlank(message = "字段名不能为空")
    private String columnField;
    
    @Schema(description = "字段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "String")
    @NotBlank(message = "字段类型不能为空")
    private String columnFieldType;
    
    @Schema(description = "列在 Excel 中的位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "列位置不能为空")
    private Integer columnIndex;
    
    @Schema(description = "表头所在的行号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "行位置不能为空")
    private Integer rowIndex;
    
    @Schema(description = "列合并的跨度", example = "1")
    private Integer columnSpan = 1;
    
    @Schema(description = "行合并的跨度", example = "1")
    private Integer rowSpan = 1;
    
    @Schema(description = "上级表头ID", example = "1")
    private Long parentColumnId;
    
    @Schema(description = "默认值", example = "默认值")
    private String columnDefaultValue;
    
    @Schema(description = "是否隐藏", example = "false")
    private Boolean columnHidden = false;
}