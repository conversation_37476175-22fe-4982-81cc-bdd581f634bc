package cn.savas.hub.module.excel.controller.admin.template.vo;

import cn.savas.hub.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Excel 模板分页查询请求 VO
 * <AUTHOR>
 * @date 2025/9/2
 */
@Schema(description = "管理后台 - Excel 动态模板分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ExcelTemplatePageReqVO extends PageParam {
    
    @Schema(description = "模板名称", example = "销售数据")
    private String templateName;
    
    @Schema(description = "模板类型(1:标准价格库、2:电缆价格库)", example = "1")
    private Integer templateType;
    
    @Schema(description = "模板描述", example = "销售")
    private String description;
}
