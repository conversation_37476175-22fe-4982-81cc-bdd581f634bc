package cn.savas.hub.module.excel.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Excel 模板响应 VO
 * <AUTHOR>
 * @date 2025/4/24 13:56
 */
@Schema(description = "管理后台 - Excel 动态模板响应 VO")
@Data
public class ExcelTemplateRespVO {
    @Schema(description = "模板唯一标识")
    private Long templateId;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板描述")
    private String description;

    @Schema(description = "模板类型(1:标准价格库、2:电缆价格库)")
    private Integer templateType;

    @Schema(description = "表头占用的行数")
    private Integer headerRows;

    @Schema(description = "数据起始行号")
    private Integer dataStartRow;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
