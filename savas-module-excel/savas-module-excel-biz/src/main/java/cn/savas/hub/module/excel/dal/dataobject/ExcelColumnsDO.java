package cn.savas.hub.module.excel.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/4/23 17:55
 */
@TableName(value = "excel_columns", autoResultMap = true)
@Data
@Accessors(chain = true)
public class ExcelColumnsDO {
    /**
     * 列唯一标识
     */
    @TableId
    private Long columnId;

    /**
     * 所属模板ID
     */
    private Long templateId;
    /**
     * Excel 中的原始列头名称，如“销售额”
     */
    private String columnName;
    /**
     * 映射字段名，如“sales_amount”
     */
    private String columnField;
    /**
     * 字段类型，如“string”、“int”
     */
    private String columnFieldType;
    /**
     * 列在 Excel 中的位置（从 0 开始）
     */
    private Integer columnIndex;
    /**
     * 表头所在的行号（支持多行表头）
     */
    private Integer rowIndex;
    /**
     * 列合并的跨度
     */
    private Integer columnSpan;
    /**
     * 行合并的跨度
     */
    private Integer rowSpan;
    /**
     * 上级表头ID（用于嵌套关系，可选）
     */
    private Long parentColumnId;
    /**
     * 默认值
     */
    private String columnDefaultValue;
    /**
     * 是否隐藏
     */
    private boolean columnHidden;
}
