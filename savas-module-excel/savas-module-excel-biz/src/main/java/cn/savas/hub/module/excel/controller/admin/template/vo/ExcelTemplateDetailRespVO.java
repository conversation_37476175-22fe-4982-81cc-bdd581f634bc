package cn.savas.hub.module.excel.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel 模板详细信息响应 VO
 * <AUTHOR>
 * @date 2025/9/2
 */
@Schema(description = "管理后台 - Excel 动态模板详细信息响应 VO")
@Data
public class ExcelTemplateDetailRespVO {
    
    @Schema(description = "模板唯一标识")
    private Long templateId;
    
    @Schema(description = "模板名称")
    private String templateName;
    
    @Schema(description = "模板描述")
    private String description;
    
    @Schema(description = "模板类型(1:标准价格库、2:电缆价格库)")
    private Integer templateType;
    
    @Schema(description = "表头占用的行数")
    private Integer headerRows;
    
    @Schema(description = "数据起始行号")
    private Integer dataStartRow;
    
    @Schema(description = "表头结构（JSON格式）")
    private String headerStructure;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    @Schema(description = "模板列信息")
    private List<ExcelColumnRespVO> columns;
    
    /**
     * Excel 列信息响应 VO
     */
    @Schema(description = "Excel 列信息")
    @Data
    public static class ExcelColumnRespVO {
        @Schema(description = "列唯一标识")
        private Long columnId;
        
        @Schema(description = "Excel 中的原始列头名称")
        private String columnName;
        
        @Schema(description = "映射字段名")
        private String columnField;
        
        @Schema(description = "字段类型")
        private String columnFieldType;
        
        @Schema(description = "列在 Excel 中的位置")
        private Integer columnIndex;
        
        @Schema(description = "表头所在的行号")
        private Integer rowIndex;
        
        @Schema(description = "列合并的跨度")
        private Integer columnSpan;
        
        @Schema(description = "行合并的跨度")
        private Integer rowSpan;
        
        @Schema(description = "上级表头ID")
        private Long parentColumnId;
        
        @Schema(description = "默认值")
        private String columnDefaultValue;
        
        @Schema(description = "是否隐藏")
        private Boolean columnHidden;
    }
}
