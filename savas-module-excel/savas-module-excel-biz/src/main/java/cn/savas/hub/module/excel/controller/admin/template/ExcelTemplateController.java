package cn.savas.hub.module.excel.controller.admin.template;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.excel.controller.admin.template.vo.*;
import cn.savas.hub.module.excel.service.template.ExcelTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;
import static cn.savas.hub.module.excel.enums.ErrorCodeConstants.EXCEL_TEMPLATE_UPLOAD_FAILED;

/**
 * Excel 模板管理控制器
 * <AUTHOR>
 * @date 2025/4/11 16:16
 */
@Tag(name = "管理后台 - Excel 动态模板")
@RestController
@RequestMapping("/excel/template")
@Validated
@Slf4j
public class ExcelTemplateController {
    @Resource
    private ExcelTemplateService excelTemplateService;

    // ========== 原有接口 ==========

    @GetMapping("/list")
    @Operation(summary = "获取模板列表")
    public CommonResult<List<ExcelTemplateRespVO>> getTemplateList() {
        return success(excelTemplateService.getTemplateList());
    }

    @GetMapping("/downloadTemplate")
    @Operation(summary = "下载模板")
    public void downloadTemplate(@RequestParam("templateId") Long templateId, HttpServletResponse response) {
        excelTemplateService.downloadTemplate(templateId, response);
    }

    // ========== 新增 CRUD 接口 ==========

    @PostMapping("/create")
    @Operation(summary = "创建 Excel 模板")
    public CommonResult<Long> createTemplate(@Valid ExcelTemplateCreateReqVO createReqVO) {
        try {
            Long templateId = excelTemplateService.createTemplate(createReqVO);
            return success(templateId);
        } catch (IOException e) {
            log.error("创建Excel模板失败，文件处理异常", e);
            return CommonResult.error(EXCEL_TEMPLATE_UPLOAD_FAILED);
        }
    }

    @PutMapping("/update")
    @Operation(summary = "更新 Excel 模板")
    public CommonResult<Boolean> updateTemplate(@Valid ExcelTemplateUpdateReqVO updateReqVO) {
        try {
            excelTemplateService.updateTemplate(updateReqVO);
            return success(true);
        } catch (IOException e) {
            log.error("更新Excel模板失败，文件处理异常", e);
            return CommonResult.error(EXCEL_TEMPLATE_UPLOAD_FAILED);
        }
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除 Excel 模板")
    @Parameter(name = "templateId", description = "模板ID", required = true, example = "1")
    public CommonResult<Boolean> deleteTemplate(@RequestParam("templateId") @NotNull(message = "模板ID不能为空") Long templateId) {
        excelTemplateService.deleteTemplate(templateId);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询 Excel 模板")
    public CommonResult<PageResult<ExcelTemplateRespVO>> getTemplatePage(@Valid ExcelTemplatePageReqVO pageReqVO) {
        PageResult<ExcelTemplateRespVO> result = excelTemplateService.getTemplatePage(pageReqVO);
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取 Excel 模板详细信息")
    @Parameter(name = "templateId", description = "模板ID", required = true, example = "1")
    public CommonResult<ExcelTemplateDetailRespVO> getTemplateDetail(@RequestParam("templateId") @NotNull(message = "模板ID不能为空") Long templateId) {
        ExcelTemplateDetailRespVO result = excelTemplateService.getTemplateDetail(templateId);
        return success(result);
    }

    // ========== 列信息管理接口 ==========

    @PostMapping("/column/create")
    @Operation(summary = "创建模板列信息")
    public CommonResult<Long> createTemplateColumn(@Valid @RequestBody ExcelColumnCreateReqVO createReqVO) {
        Long columnId = excelTemplateService.createTemplateColumn(createReqVO);
        return success(columnId);
    }

    @PutMapping("/column/update")
    @Operation(summary = "更新模板列信息")
    public CommonResult<Boolean> updateTemplateColumn(@Valid @RequestBody ExcelColumnUpdateReqVO updateReqVO) {
        excelTemplateService.updateTemplateColumn(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/column/delete")
    @Operation(summary = "删除模板列信息")
    @Parameter(name = "columnId", description = "列ID", required = true, example = "1")
    public CommonResult<Boolean> deleteTemplateColumn(@RequestParam("columnId") @NotNull(message = "列ID不能为空") Long columnId) {
        excelTemplateService.deleteTemplateColumn(columnId);
        return success(true);
    }

    @PostMapping("/column/batch-save")
    @Operation(summary = "批量保存模板列信息")
    public CommonResult<Boolean> batchSaveTemplateColumns(@Valid @RequestBody ExcelColumnBatchSaveReqVO batchSaveReqVO) {
        excelTemplateService.batchSaveTemplateColumns(batchSaveReqVO);
        return success(true);
    }

    @GetMapping("/column/list")
    @Operation(summary = "获取模板列信息列表")
    @Parameter(name = "templateId", description = "模板ID", required = true, example = "1")
    public CommonResult<List<ExcelTemplateDetailRespVO.ExcelColumnRespVO>> getTemplateColumns(@RequestParam("templateId") @NotNull(message = "模板ID不能为空") Long templateId) {
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> result = excelTemplateService.getTemplateColumns(templateId);
        return success(result);
    }

    @PostMapping("/column/import")
    @Operation(summary = "导入列信息")
    public CommonResult<Boolean> importTemplateColumns(@Valid @RequestBody ExcelColumnImportReqVO importReqVO) {
        excelTemplateService.importTemplateColumns(importReqVO);
        return success(true);
    }

    @GetMapping("/column/export")
    @Operation(summary = "导出列信息")
    @Parameter(name = "templateId", description = "模板ID", required = true, example = "1")
    public void exportTemplateColumns(@RequestParam("templateId") @NotNull(message = "模板ID不能为空") Long templateId, HttpServletResponse response) {
        excelTemplateService.exportTemplateColumns(templateId, response);
    }
}
