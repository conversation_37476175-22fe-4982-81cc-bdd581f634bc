package cn.savas.hub.module.excel.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 18:06
 */
@Mapper
public interface ExcelColumnsMapper extends BaseMapperX<ExcelColumnsDO> {
    default List<ExcelColumnsDO> selectByTemplateId(Long templateId){
        return selectList(new LambdaQueryWrapper<ExcelColumnsDO>()
                .eq(ExcelColumnsDO::getTemplateId, templateId)
                .orderByAsc(ExcelColumnsDO::getColumnIndex));
    }
}
