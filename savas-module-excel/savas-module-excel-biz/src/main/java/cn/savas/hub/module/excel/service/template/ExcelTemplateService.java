package cn.savas.hub.module.excel.service.template;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.excel.controller.admin.template.vo.*;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Excel 模板服务接口
 * <AUTHOR>
 * @date 2025/4/11 16:23
 */
public interface ExcelTemplateService {

    // ========== 原有方法 ==========

    /**
     * 解析 Excel 文件
     */
    List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException;

    /**
     * 获取模板列表
     */
    List<ExcelTemplateRespVO> getTemplateList();

    /**
     * 获取模板列信息
     */
    List<ExcelColumnsDO> getTemplateColumnsApi(Long templateId);

    /**
     * 下载模板
     */
    void downloadTemplate(Long templateId, HttpServletResponse response);

    /**
     * 根据模板名称获取列信息
     */
    List<ExcelColumnsDO> getTemplateColumnsByName(String templateName);

    /**
     * 根据模板名称获取模板
     */
    ExcelTemplatesDO getTemplateByName(String templateName);

    /**
     * 根据模板类型获取模板列表
     */
    List<ExcelTemplatesDO> getTemplateByType(Integer templateType);

    /**
     * 根据ID获取模板
     */
    ExcelTemplatesDO getTemplateById(Long templateId);

    // ========== 新增 CRUD 方法 ==========

    /**
     * 创建 Excel 模板
     * @param createReqVO 创建请求
     * @return 模板ID
     */
    Long createTemplate(ExcelTemplateCreateReqVO createReqVO) throws IOException;

    /**
     * 更新 Excel 模板
     * @param updateReqVO 更新请求
     */
    void updateTemplate(ExcelTemplateUpdateReqVO updateReqVO) throws IOException;

    /**
     * 删除 Excel 模板
     * @param templateId 模板ID
     */
    void deleteTemplate(Long templateId);

    /**
     * 分页查询 Excel 模板
     * @param pageReqVO 分页查询请求
     * @return 分页结果
     */
    PageResult<ExcelTemplateRespVO> getTemplatePage(ExcelTemplatePageReqVO pageReqVO);

    /**
     * 获取 Excel 模板详细信息
     * @param templateId 模板ID
     * @return 模板详细信息
     */
    ExcelTemplateDetailRespVO getTemplateDetail(Long templateId);

    // ========== 列信息管理方法 ==========

    /**
     * 创建模板列信息
     * @param createReqVO 创建请求
     * @return 列ID
     */
    Long createTemplateColumn(ExcelColumnCreateReqVO createReqVO);

    /**
     * 更新模板列信息
     * @param updateReqVO 更新请求
     */
    void updateTemplateColumn(ExcelColumnUpdateReqVO updateReqVO);

    /**
     * 删除模板列信息
     * @param columnId 列ID
     */
    void deleteTemplateColumn(Long columnId);

    /**
     * 批量保存模板列信息
     * @param batchSaveReqVO 批量保存请求
     */
    void batchSaveTemplateColumns(ExcelColumnBatchSaveReqVO batchSaveReqVO);

    /**
     * 获取模板列信息列表
     * @param templateId 模板ID
     * @return 列信息列表
     */
    List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> getTemplateColumns(Long templateId);

    /**
     * 导入列信息
     * @param importReqVO 导入请求
     */
    void importTemplateColumns(ExcelColumnImportReqVO importReqVO);

    /**
     * 导出列信息
     * @param templateId 模板ID
     * @param response HTTP响应
     */
    void exportTemplateColumns(Long templateId, HttpServletResponse response);
}
