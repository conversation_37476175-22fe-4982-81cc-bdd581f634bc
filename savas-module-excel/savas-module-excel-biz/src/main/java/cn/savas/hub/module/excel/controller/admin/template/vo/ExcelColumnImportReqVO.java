package cn.savas.hub.module.excel.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Excel 列信息导入请求 VO
 * <AUTHOR>
 * @date 2025/9/2
 */
@Schema(description = "管理后台 - Excel 列信息导入请求 VO")
@Data
public class ExcelColumnImportReqVO {
    
    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "模板ID不能为空")
    private Long templateId;
    
    @Schema(description = "导入的列信息数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "导入数据不能为空")
    private List<Map<String, Object>> data;
    
    @Schema(description = "是否覆盖现有数据", example = "false")
    private Boolean overwrite = false;
}