package cn.savas.hub.module.excel.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/4/23 17:55
 */
@TableName(value = "excel_templates")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ExcelTemplatesDO extends BaseDO {
    /**
     * 模板唯一标识
     */
    @TableId
    private Long templateId;
    /**
     * 模板名称，如“销售数据模板”
     */
    private String templateName;
    /**
     * 模板描述
     */
    private String description;
    /**
     * 表头占用的行数
     */
    private Integer headerRows;
    /**
     * 数据起始行号
     */
    private Integer dataStartRow;
    /**
     * 表头结构（JSON格式，可选）
     */
    private String headerStructure;
    /**
     * DB文件
     */
    private byte[] dbFile;
    /**
     *模版类型(1:标准价格库、2:电缆价格库)
     */
    private Integer templateType;
}
