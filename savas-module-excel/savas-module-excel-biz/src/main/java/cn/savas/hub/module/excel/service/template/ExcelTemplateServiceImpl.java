package cn.savas.hub.module.excel.service.template;

import cn.hutool.core.util.StrUtil;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.excel.core.util.ExcelUtils;
import cn.savas.hub.module.excel.controller.admin.template.vo.*;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import cn.savas.hub.module.excel.dal.mapper.ExcelColumnsMapper;
import cn.savas.hub.module.excel.dal.mapper.ExcelTemplatesMapper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.excel.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:23
 */
@Service
@Slf4j
public class ExcelTemplateServiceImpl implements ExcelTemplateService{
    @Resource
    private ExcelTemplatesMapper templateRepo;
    @Resource
    private ExcelColumnsMapper columnRepo;
    @Override
    public List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException {
        // 1. 加载模板
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        List<ExcelColumnsDO> columns = columnRepo.selectByTemplateId(templateId);
        List<Map<String, Object>> dataList = new ArrayList<>();
        // 2. 配置 EasyExcel 解析
        EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                        // 跳过表头
                        if (context.readRowHolder().getRowIndex() < template.getDataStartRow() - 1) {
                            return;
                        }
                        // 映射列到字段
                        Map<String, Object> mappedRow = new HashMap<>();
                        for (ExcelColumnsDO col : columns) {
                            // 如果有默认值
                            if (col.getColumnDefaultValue() != null && !col.getColumnDefaultValue().isEmpty()) {
                                mappedRow.put(col.getColumnField(), convertValue(col.getColumnDefaultValue(), col.getColumnFieldType()));
                                continue;
                            }
                            // 获取列索引对应的值
                            String value = rowData.get(col.getColumnIndex());
                            if (col.getColumnField() != null) {
                                mappedRow.put(col.getColumnField(), convertValue(value, col.getColumnFieldType()));
                            }
                        }
                        dataList.add(mappedRow);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        // 3. 处理解析后的数据
                    }
                })
                .headRowNumber(template.getHeaderRows()) // 设置表头行数
                .sheet()
                .doRead();
        return dataList;
    }

    @Override
    public List<ExcelTemplateRespVO> getTemplateList() {
        List<ExcelTemplatesDO> source = templateRepo.selectList(new LambdaQueryWrapper<ExcelTemplatesDO>()
                .eq(ExcelTemplatesDO::getTemplateType, 1)); // 1：标准价格库，2：电缆价格库
        return BeanUtils.toBean(source, ExcelTemplateRespVO.class);
    }

    @Override
    public List<ExcelColumnsDO> getTemplateColumnsApi(Long templateId) {
        return columnRepo.selectByTemplateId(templateId);
    }

    @Override
    public void downloadTemplate(Long templateId, HttpServletResponse response) {
        // 1. 加载模板和列信息
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        List<ExcelColumnsDO> columns = columnRepo.selectByTemplateId(templateId);

        // 2. 准备表头数据
        Map<Long, ExcelColumnsDO> columnIdMap = new HashMap<>();
        for (ExcelColumnsDO column : columns) {
            columnIdMap.put(column.getColumnId(), column);
        }
        // 构建表头
        List<List<String>> head = new LinkedList<>();
        // 获取所有叶子节点
        List<ExcelColumnsDO> leafColumns = new ArrayList<>();
        for (ExcelColumnsDO column : columns) {
            if (column.getColumnSpan() == 1) {
                leafColumns.add(column);
            }
        }
        // 遍历叶子节点，构建表头
        for (ExcelColumnsDO column : leafColumns) {
            List<String> rowHead = new LinkedList<>();
            buildHeader(column, columnIdMap, rowHead);
            head.add(column.getColumnIndex(), rowHead);
        }
        // 遍历叶子节点, 填充默认值
        List<List<Object>> defaultRowList = new ArrayList<>(1);
        List<Object> defaultRow = new ArrayList<>(head.size());
        defaultRowList.add(defaultRow);
        for (ExcelColumnsDO column : leafColumns) {
            Object defaultValue = convertValue(column.getColumnDefaultValue(), column.getColumnFieldType());
            defaultRow.add(defaultValue == null ? "" : defaultValue);
        }
        // 3. 导出Excel
        try {
            ExcelUtils.write(response, template.getTemplateName() + ".xlsx", template.getTemplateName(), head, defaultRowList);
        } catch (IOException e) {
            log.error("下载模板失败，templateId: {}, error: {}", templateId, e.getMessage(), e);
            throw exception(EXCEL_TEMPLATE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public List<ExcelColumnsDO> getTemplateColumnsByName(String templateName) {
        ExcelTemplatesDO template = templateRepo.selectOne(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .select(ExcelTemplatesDO::getTemplateId)
                        .eq(ExcelTemplatesDO::getTemplateName, templateName)
        );
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return columnRepo.selectByTemplateId(template.getTemplateId());
    }

    @Override
    public ExcelTemplatesDO getTemplateByName(String templateName) {
        ExcelTemplatesDO template = templateRepo.selectOne(ExcelTemplatesDO::getTemplateName, templateName);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    @Override
    public List<ExcelTemplatesDO> getTemplateByType(Integer templateType) {
        List<ExcelTemplatesDO> templates = templateRepo.selectList(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .eq(ExcelTemplatesDO::getTemplateType, templateType)
        );
        if (templates.isEmpty()) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return templates;
    }

    @Override
    public ExcelTemplatesDO getTemplateById(Long templateId) {
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    /**
     * 递归构建表头
     *
     * @param column      当前列
     * @param columnIdMap 列ID映射
     *    * @param rowHead     表头列表
     */
    private void buildHeader(ExcelColumnsDO column, Map<Long, ExcelColumnsDO> columnIdMap, List<String> rowHead) {
        rowHead.add(0, column.getColumnName());
        ExcelColumnsDO parent = columnIdMap.get(column.getParentColumnId());
        // 如果有父级列，则递归构建父级列的表头
        if (parent != null) {
            buildHeader(parent, columnIdMap, rowHead);
        }
    }

    private Object convertValue(String value, String fieldType) {
        // 根据 field_type 转换数据类型
        // 示例：string、int、date 等
        if (value == null || value.isEmpty()) {
            return null;
        }
        if ("decimal".equalsIgnoreCase(fieldType)) {
            return new BigDecimal(value);
        } else if ("string".equalsIgnoreCase(fieldType)) {
            return value;
        }else if ("long".equalsIgnoreCase(fieldType)) {
            return Long.parseLong(value);
        }else if ("integer".equalsIgnoreCase(fieldType)) {
            return Integer.parseInt(value);
        }
        return value;
    }

    // ========== 新增 CRUD 方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTemplate(ExcelTemplateCreateReqVO createReqVO) throws IOException {
        // 1. 校验模板名称是否已存在
        ExcelTemplatesDO existTemplate = templateRepo.selectOne(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .eq(ExcelTemplatesDO::getTemplateName, createReqVO.getTemplateName())
        );
        if (existTemplate != null) {
            throw exception(EXCEL_TEMPLATE_NAME_EXISTS);
        }

        // 2. 校验文件格式
        MultipartFile file = createReqVO.getTemplateFile();
        if (file == null || file.isEmpty()) {
            throw exception(EXCEL_TEMPLATE_UPLOAD_FAILED);
        }

        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName) || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw exception(EXCEL_TEMPLATE_FILE_FORMAT_ERROR);
        }

        // 3. 创建模板记录
        ExcelTemplatesDO template = new ExcelTemplatesDO();
        template.setTemplateName(createReqVO.getTemplateName());
        template.setDescription(createReqVO.getDescription());
        template.setTemplateType(createReqVO.getTemplateType());
        template.setHeaderRows(createReqVO.getHeaderRows());
        template.setDataStartRow(createReqVO.getDataStartRow());
        template.setHeaderStructure(StringUtils.isEmpty(createReqVO.getHeaderStructure())? null : createReqVO.getHeaderStructure());

        try {
            // 4. 保存文件内容
            template.setDbFile(file.getBytes());
        } catch (IOException e) {
            log.error("读取模板文件失败", e);
            throw exception(EXCEL_TEMPLATE_UPLOAD_FAILED);
        }

        // 5. 插入数据库
        templateRepo.insert(template);

        log.info("创建Excel模板成功，模板ID: {}, 模板名称: {}", template.getTemplateId(), template.getTemplateName());
        return template.getTemplateId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(ExcelTemplateUpdateReqVO updateReqVO) throws IOException {
        // 1. 校验模板是否存在
        ExcelTemplatesDO existTemplate = templateRepo.selectById(updateReqVO.getTemplateId());
        if (existTemplate == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 校验模板名称是否重复（排除自己）
        ExcelTemplatesDO duplicateTemplate = templateRepo.selectOne(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .eq(ExcelTemplatesDO::getTemplateName, updateReqVO.getTemplateName())
                        .ne(ExcelTemplatesDO::getTemplateId, updateReqVO.getTemplateId())
        );
        if (duplicateTemplate != null) {
            throw exception(EXCEL_TEMPLATE_NAME_EXISTS);
        }

        // 3. 更新模板信息
        ExcelTemplatesDO template = new ExcelTemplatesDO();
        template.setTemplateId(updateReqVO.getTemplateId());
        template.setTemplateName(updateReqVO.getTemplateName());
        template.setDescription(updateReqVO.getDescription());
        template.setTemplateType(updateReqVO.getTemplateType());
        template.setHeaderRows(updateReqVO.getHeaderRows());
        template.setDataStartRow(updateReqVO.getDataStartRow());
        template.setHeaderStructure(StringUtils.isEmpty(updateReqVO.getHeaderStructure())? null : updateReqVO.getHeaderStructure());

        // 4. 如果有新文件，更新文件内容
        MultipartFile file = updateReqVO.getTemplateFile();
        if (file != null && !file.isEmpty()) {
            String fileName = file.getOriginalFilename();
            if (StrUtil.isBlank(fileName) || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                throw exception(EXCEL_TEMPLATE_FILE_FORMAT_ERROR);
            }

            try {
                template.setDbFile(file.getBytes());
            } catch (IOException e) {
                log.error("读取模板文件失败", e);
                throw exception(EXCEL_TEMPLATE_UPLOAD_FAILED);
            }
        }

        // 5. 更新数据库
        templateRepo.updateById(template);

        log.info("更新Excel模板成功，模板ID: {}, 模板名称: {}", template.getTemplateId(), template.getTemplateName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplate(Long templateId) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 删除模板相关的列信息
        columnRepo.delete(new LambdaQueryWrapper<ExcelColumnsDO>()
                .eq(ExcelColumnsDO::getTemplateId, templateId));

        // 3. 删除模板
        templateRepo.deleteById(templateId);

        log.info("删除Excel模板成功，模板ID: {}, 模板名称: {}", templateId, template.getTemplateName());
    }

    @Override
    public PageResult<ExcelTemplateRespVO> getTemplatePage(ExcelTemplatePageReqVO pageReqVO) {
        // 1. 构建查询条件
        LambdaQueryWrapper<ExcelTemplatesDO> queryWrapper = new LambdaQueryWrapper<ExcelTemplatesDO>()
                .like(StrUtil.isNotBlank(pageReqVO.getTemplateName()),
                      ExcelTemplatesDO::getTemplateName, pageReqVO.getTemplateName())
                .like(StrUtil.isNotBlank(pageReqVO.getDescription()),
                      ExcelTemplatesDO::getDescription, pageReqVO.getDescription())
                .eq(pageReqVO.getTemplateType() != null,
                    ExcelTemplatesDO::getTemplateType, pageReqVO.getTemplateType())
                .orderByDesc(ExcelTemplatesDO::getCreateTime);

        // 2. 分页查询
        IPage<ExcelTemplatesDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<ExcelTemplatesDO> result = templateRepo.selectPage(page, queryWrapper);

        // 3. 转换结果
        List<ExcelTemplateRespVO> list = BeanUtils.toBean(result.getRecords(), ExcelTemplateRespVO.class);
        return new PageResult<>(list, result.getTotal());
    }

    @Override
    public ExcelTemplateDetailRespVO getTemplateDetail(Long templateId) {
        // 1. 查询模板基本信息
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 查询模板列信息
        List<ExcelColumnsDO> columns = columnRepo.selectByTemplateId(templateId);

        // 3. 转换结果
        ExcelTemplateDetailRespVO result = BeanUtils.toBean(template, ExcelTemplateDetailRespVO.class);
        List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> columnVOs = BeanUtils.toBean(columns, ExcelTemplateDetailRespVO.ExcelColumnRespVO.class);
        result.setColumns(columnVOs);

        return result;
    }

    // ========== 列信息管理方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTemplateColumn(ExcelColumnCreateReqVO createReqVO) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(createReqVO.getTemplateId());
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 校验列位置是否重复
        ExcelColumnsDO existColumn = columnRepo.selectOne(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, createReqVO.getTemplateId())
                        .eq(ExcelColumnsDO::getColumnIndex, createReqVO.getColumnIndex())
                        .eq(ExcelColumnsDO::getRowIndex, createReqVO.getRowIndex())
        );
        if (existColumn != null) {
            throw exception(EXCEL_COLUMN_POSITION_EXISTS);
        }

        // 3. 校验字段名是否重复
        ExcelColumnsDO duplicateField = columnRepo.selectOne(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, createReqVO.getTemplateId())
                        .eq(ExcelColumnsDO::getColumnField, createReqVO.getColumnField())
        );
        if (duplicateField != null) {
            throw exception(EXCEL_COLUMN_FIELD_EXISTS);
        }

        // 4. 创建列信息
        ExcelColumnsDO column = BeanUtils.toBean(createReqVO, ExcelColumnsDO.class);
        columnRepo.insert(column);

        log.info("创建模板列信息成功，列ID: {}, 模板ID: {}, 列名: {}",
                column.getColumnId(), createReqVO.getTemplateId(), createReqVO.getColumnName());
        return column.getColumnId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplateColumn(ExcelColumnUpdateReqVO updateReqVO) {
        // 1. 校验列是否存在
        ExcelColumnsDO existColumn = columnRepo.selectById(updateReqVO.getColumnId());
        if (existColumn == null) {
            throw exception(EXCEL_COLUMN_NOT_EXISTS);
        }

        // 2. 校验列位置是否重复（排除自己）
        ExcelColumnsDO duplicatePosition = columnRepo.selectOne(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, existColumn.getTemplateId())
                        .eq(ExcelColumnsDO::getColumnIndex, updateReqVO.getColumnIndex())
                        .eq(ExcelColumnsDO::getRowIndex, updateReqVO.getRowIndex())
                        .ne(ExcelColumnsDO::getColumnId, updateReqVO.getColumnId())
        );
        if (duplicatePosition != null) {
            throw exception(EXCEL_COLUMN_POSITION_EXISTS);
        }

        // 3. 校验字段名是否重复（排除自己）
        ExcelColumnsDO duplicateField = columnRepo.selectOne(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, existColumn.getTemplateId())
                        .eq(ExcelColumnsDO::getColumnField, updateReqVO.getColumnField())
                        .ne(ExcelColumnsDO::getColumnId, updateReqVO.getColumnId())
        );
        if (duplicateField != null) {
            throw exception(EXCEL_COLUMN_FIELD_EXISTS);
        }

        // 4. 更新列信息
        ExcelColumnsDO column = BeanUtils.toBean(updateReqVO, ExcelColumnsDO.class);
        columnRepo.updateById(column);

        log.info("更新模板列信息成功，列ID: {}, 列名: {}", updateReqVO.getColumnId(), updateReqVO.getColumnName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplateColumn(Long columnId) {
        // 1. 校验列是否存在
        ExcelColumnsDO column = columnRepo.selectById(columnId);
        if (column == null) {
            throw exception(EXCEL_COLUMN_NOT_EXISTS);
        }

        // 2. 检查是否有子列依赖
        List<ExcelColumnsDO> childColumns = columnRepo.selectList(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getParentColumnId, columnId)
        );
        if (!childColumns.isEmpty()) {
            throw exception(EXCEL_COLUMN_HAS_CHILDREN);
        }

        // 3. 删除列信息
        columnRepo.deleteById(columnId);

        log.info("删除模板列信息成功，列ID: {}, 列名: {}", columnId, column.getColumnName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveTemplateColumns(ExcelColumnBatchSaveReqVO batchSaveReqVO) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(batchSaveReqVO.getTemplateId());
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 获取现有列信息
        List<ExcelColumnsDO> existingColumns = columnRepo.selectList(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, batchSaveReqVO.getTemplateId())
        );

        // 3. 处理批量保存
        List<Long> updatedColumnIds = new ArrayList<>();

        for (ExcelColumnBatchSaveReqVO.ExcelColumnBatchItemVO item : batchSaveReqVO.getColumns()) {
            if (item.getColumnId() != null) {
                // 更新现有列
                ExcelColumnUpdateReqVO updateReqVO = BeanUtils.toBean(item, ExcelColumnUpdateReqVO.class);
                updateTemplateColumn(updateReqVO);
                updatedColumnIds.add(item.getColumnId());
            } else {
                // 创建新列
                ExcelColumnCreateReqVO createReqVO = BeanUtils.toBean(item, ExcelColumnCreateReqVO.class);
                createReqVO.setTemplateId(batchSaveReqVO.getTemplateId());
                Long columnId = createTemplateColumn(createReqVO);
                updatedColumnIds.add(columnId);
            }
        }

        // 4. 删除不在更新列表中的列
        for (ExcelColumnsDO existingColumn : existingColumns) {
            if (!updatedColumnIds.contains(existingColumn.getColumnId())) {
                deleteTemplateColumn(existingColumn.getColumnId());
            }
        }

        log.info("批量保存模板列信息成功，模板ID: {}, 处理列数: {}",
                batchSaveReqVO.getTemplateId(), batchSaveReqVO.getColumns().size());
    }

    @Override
    public List<ExcelTemplateDetailRespVO.ExcelColumnRespVO> getTemplateColumns(Long templateId) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 查询列信息
        List<ExcelColumnsDO> columns = columnRepo.selectList(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, templateId)
                        .orderByAsc(ExcelColumnsDO::getRowIndex)
                        .orderByAsc(ExcelColumnsDO::getColumnIndex)
        );

        // 3. 转换结果
        return BeanUtils.toBean(columns, ExcelTemplateDetailRespVO.ExcelColumnRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTemplateColumns(ExcelColumnImportReqVO importReqVO) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(importReqVO.getTemplateId());
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 如果需要覆盖，先删除现有列信息
        if (importReqVO.getOverwrite()) {
            columnRepo.delete(new LambdaQueryWrapper<ExcelColumnsDO>()
                    .eq(ExcelColumnsDO::getTemplateId, importReqVO.getTemplateId()));
        }

        // 3. 处理导入数据
        for (Map<String, Object> data : importReqVO.getData()) {
            ExcelColumnCreateReqVO createReqVO = new ExcelColumnCreateReqVO();
            createReqVO.setTemplateId(importReqVO.getTemplateId());
            createReqVO.setColumnName(String.valueOf(data.get("columnName")));
            createReqVO.setColumnField(String.valueOf(data.get("columnField")));
            createReqVO.setColumnFieldType(String.valueOf(data.get("columnFieldType")));
            createReqVO.setColumnIndex(Integer.valueOf(String.valueOf(data.get("columnIndex"))));
            createReqVO.setRowIndex(Integer.valueOf(String.valueOf(data.get("rowIndex"))));
            createReqVO.setColumnSpan(data.get("columnSpan") != null ?
                    Integer.valueOf(String.valueOf(data.get("columnSpan"))) : 1);
            createReqVO.setRowSpan(data.get("rowSpan") != null ?
                    Integer.valueOf(String.valueOf(data.get("rowSpan"))) : 1);
            createReqVO.setParentColumnId(data.get("parentColumnId") != null ?
                    Long.valueOf(String.valueOf(data.get("parentColumnId"))) : null);
            createReqVO.setColumnDefaultValue(String.valueOf(data.get("columnDefaultValue")));
            createReqVO.setColumnHidden(Boolean.valueOf(String.valueOf(data.get("columnHidden"))));

            try {
                createTemplateColumn(createReqVO);
            } catch (Exception e) {
                log.warn("导入列信息失败，跳过该条记录: {}", createReqVO.getColumnName(), e);
            }
        }

        log.info("导入模板列信息成功，模板ID: {}, 导入条数: {}",
                importReqVO.getTemplateId(), importReqVO.getData().size());
    }

    @Override
    public void exportTemplateColumns(Long templateId, HttpServletResponse response) {
        // 1. 校验模板是否存在
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }

        // 2. 查询列信息
        List<ExcelColumnsDO> columns = columnRepo.selectList(
                new LambdaQueryWrapper<ExcelColumnsDO>()
                        .eq(ExcelColumnsDO::getTemplateId, templateId)
                        .orderByAsc(ExcelColumnsDO::getRowIndex)
                        .orderByAsc(ExcelColumnsDO::getColumnIndex)
        );

        // 3. 准备导出数据
        List<List<String>> head = Arrays.asList(
                Arrays.asList("列名称"),
                Arrays.asList("字段名"),
                Arrays.asList("字段类型"),
                Arrays.asList("列位置"),
                Arrays.asList("行位置"),
                Arrays.asList("列跨度"),
                Arrays.asList("行跨度"),
                Arrays.asList("上级表头ID"),
                Arrays.asList("默认值"),
                Arrays.asList("是否隐藏")
        );

        List<List<Object>> data = new ArrayList<>();
        for (ExcelColumnsDO column : columns) {
            List<Object> row = Arrays.asList(
                    column.getColumnName(),
                    column.getColumnField(),
                    column.getColumnFieldType(),
                    column.getColumnIndex(),
                    column.getRowIndex(),
                    column.getColumnSpan(),
                    column.getRowSpan(),
                    column.getParentColumnId(),
                    column.getColumnDefaultValue(),
                    column.isColumnHidden() ? "是" : "否"
            );
            data.add(row);
        }

        // 4. 导出Excel
        try {
            String fileName = template.getTemplateName() + "_列信息.xlsx";
            ExcelUtils.write(response, fileName, "列信息", head, data);
        } catch (IOException e) {
            log.error("导出列信息失败，templateId: {}, error: {}", templateId, e.getMessage(), e);
            throw exception(EXCEL_TEMPLATE_EXPORT_FAILED);
        }
    }
}
