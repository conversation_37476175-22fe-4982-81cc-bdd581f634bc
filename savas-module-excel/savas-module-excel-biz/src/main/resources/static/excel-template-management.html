<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel 模板管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- x-spreadsheet CSS -->
    <link href="https://unpkg.com/x-data-spreadsheet@1.1.9/dist/xspreadsheet.css" rel="stylesheet">
    <style>
        .table-container {
            margin-top: 20px;
        }
        .btn-group-sm .btn {
            margin-right: 5px;
        }
        .modal-header {
            background-color: #f8f9fa;
        }
        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        .file-upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f1ff;
        }
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .column-editor-container {
            height: 500px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-top: 15px;
        }
        .column-toolbar {
            background-color: #f8f9fa;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }
        .column-preview {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-top: 15px;
        }
        .field-type-badge {
            font-size: 0.75em;
        }
        .column-management-tabs {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center py-3">
                    <h2><i class="fas fa-file-excel text-success"></i> Excel 模板管理</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#templateModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i> 新增模板
                    </button>
                </div>

                <!-- 搜索表单 -->
                <div class="search-form">
                    <form id="searchForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="searchTemplateName" class="form-label">模板名称</label>
                            <input type="text" class="form-control" id="searchTemplateName" placeholder="请输入模板名称">
                        </div>
                        <div class="col-md-3">
                            <label for="searchTemplateType" class="form-label">模板类型</label>
                            <select class="form-select" id="searchTemplateType">
                                <option value="">全部类型</option>
                                <option value="1">标准价格库</option>
                                <option value="2">电缆价格库</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchDescription" class="form-label">模板描述</label>
                            <input type="text" class="form-control" id="searchDescription" placeholder="请输入描述关键词">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-primary me-2" onclick="searchTemplates()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 模板列表 -->
                <div class="table-container">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">模板列表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>模板ID</th>
                                            <th>模板名称</th>
                                            <th>模板描述</th>
                                            <th>模板类型</th>
                                            <th>表头行数</th>
                                            <th>数据起始行</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="templateTableBody">
                                        <!-- 数据将通过 JavaScript 动态加载 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <nav aria-label="模板分页">
                                <ul class="pagination justify-content-center" id="pagination">
                                    <!-- 分页按钮将通过 JavaScript 动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板编辑模态框 -->
    <div class="modal fade" id="templateModal" tabindex="-1" aria-labelledby="templateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="templateModalLabel">新增模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="templateForm" enctype="multipart/form-data">
                        <input type="hidden" id="templateId" name="templateId">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="templateName" class="form-label">模板名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="templateName" name="templateName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="templateType" class="form-label">模板类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="templateType" name="templateType" required>
                                        <option value="">请选择模板类型</option>
                                        <option value="1">标准价格库</option>
                                        <option value="2">电缆价格库</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">模板描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入模板描述"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="headerRows" class="form-label">表头行数 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="headerRows" name="headerRows" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dataStartRow" class="form-label">数据起始行 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="dataStartRow" name="dataStartRow" min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="headerStructure" class="form-label">表头结构 (JSON格式)</label>
                            <textarea class="form-control" id="headerStructure" name="headerStructure" rows="3" placeholder='{"headers": []}'></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">模板文件 <span class="text-danger" id="fileRequired">*</span></label>
                            <div class="file-upload-area" onclick="document.getElementById('templateFile').click()">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="mb-0">点击选择文件或拖拽文件到此处</p>
                                <small class="text-muted">支持 .xlsx, .xls 格式</small>
                                <input type="file" class="d-none" id="templateFile" name="templateFile" accept=".xlsx,.xls">
                            </div>
                            <div id="fileInfo" class="mt-2" style="display: none;">
                                <small class="text-success">
                                    <i class="fas fa-file-excel"></i>
                                    <span id="fileName"></span>
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTemplate()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 列信息管理模态框 -->
    <div class="modal fade" id="columnManagementModal" tabindex="-1" aria-labelledby="columnManagementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="columnManagementModalLabel">
                        <i class="fas fa-columns"></i> 列信息管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="currentTemplateId">

                    <!-- 操作工具栏 -->
                    <div class="column-toolbar">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success btn-sm" onclick="addColumn()">
                                        <i class="fas fa-plus"></i> 添加列
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteSelectedColumns()">
                                        <i class="fas fa-trash"></i> 删除选中
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" onclick="importColumns()">
                                        <i class="fas fa-file-import"></i> 导入
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="exportColumns()">
                                        <i class="fas fa-file-export"></i> 导出
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="previewHeader()">
                                        <i class="fas fa-eye"></i> 预览表头
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" onclick="debugColumnData()">
                                        <i class="fas fa-bug"></i> 调试
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button type="button" class="btn btn-primary" onclick="saveAllColumns()">
                                    <i class="fas fa-save"></i> 保存所有更改
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 选项卡 -->
                    <div class="column-management-tabs">
                        <ul class="nav nav-tabs" id="columnTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="spreadsheet-tab" data-bs-toggle="tab" data-bs-target="#spreadsheet-pane" type="button" role="tab">
                                    <i class="fas fa-table"></i> 表格编辑
                                </button>
                            </li>

                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview-pane" type="button" role="tab">
                                    <i class="fas fa-eye"></i> 预览
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="columnTabContent">
                            <!-- 表格编辑面板 -->
                            <div class="tab-pane fade show active" id="spreadsheet-pane" role="tabpanel">
                                <div class="column-editor-container">
                                    <div id="columnSpreadsheet"></div>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        提示：双击单元格进行编辑，点击行号选择行，使用工具栏按钮进行增删操作。字段类型支持：String、Integer、Long、Double、Boolean、Date
                                    </small>
                                </div>
                            </div>



                            <!-- 预览面板 -->
                            <div class="tab-pane fade" id="preview-pane" role="tabpanel">
                                <div class="column-preview mt-3">
                                    <div id="headerPreview">
                                        <!-- 表头预览将动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入列信息模态框 -->
    <div class="modal fade" id="importColumnsModal" tabindex="-1" aria-labelledby="importColumnsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importColumnsModalLabel">导入列信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="importForm">
                        <div class="mb-3">
                            <label class="form-label">选择文件</label>
                            <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls,.csv">
                            <small class="text-muted">支持 Excel (.xlsx, .xls) 和 CSV 格式</small>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="overwriteExisting">
                            <label class="form-check-label" for="overwriteExisting">
                                覆盖现有数据
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="processImport()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- x-spreadsheet JS -->
    <script src="https://unpkg.com/x-data-spreadsheet@1.1.9/dist/xspreadsheet.js"></script>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let isEditMode = false;

        // 列管理相关变量
        let columnSpreadsheet = null;
        let currentColumns = [];
        let selectedColumnIndex = -1;



        // 模板类型映射
        const templateTypeMap = {
            1: '标准价格库',
            2: '电缆价格库'
        };

        // 加载模板列表
        function loadTemplates(page = 1) {
            currentPage = page;

            const searchData = {
                pageNo: page,
                pageSize: pageSize,
                templateName: $('#searchTemplateName').val(),
                templateType: $('#searchTemplateType').val(),
                description: $('#searchDescription').val()
            };

            $.ajax({
                url: 'admin-api/excel/template/page',
                method: 'GET',
                data: searchData,
                success: function(response) {
                    if (response.code === 0) {
                        renderTemplateTable(response.data.list);
                        renderPagination(response.data.total, page);
                    } else {
                        showAlert('加载模板列表失败：' + response.msg, 'danger');
                    }
                },
                error: function() {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            });
        }

        // 渲染模板表格
        function renderTemplateTable(templates) {
            const tbody = $('#templateTableBody');
            tbody.empty();

            if (templates.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="8" class="text-center text-muted">暂无数据</td>
                    </tr>
                `);
                return;
            }

            templates.forEach(template => {
                const row = `
                    <tr>
                        <td>${template.templateId}</td>
                        <td>${template.templateName}</td>
                        <td>${template.description || '-'}</td>
                        <td><span class="badge bg-info">${templateTypeMap[template.templateType] || '未知'}</span></td>
                        <td>${template.headerRows}</td>
                        <td>${template.dataStartRow}</td>
                        <td>${formatDateTime(template.createTime)}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="viewTemplate(${template.templateId})" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="editTemplate(${template.templateId})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="manageColumns(${template.templateId}, '${template.templateName}')" title="列管理">
                                    <i class="fas fa-columns"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="downloadTemplate(${template.templateId})" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(${template.templateId}, '${template.templateName}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 渲染分页
        function renderPagination(total, currentPage) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页
            const prevDisabled = currentPage === 1 ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="#" onclick="loadTemplates(${currentPage - 1})">上一页</a>
                </li>
            `);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="#" onclick="loadTemplates(${i})">${i}</a>
                    </li>
                `);
            }

            // 下一页
            const nextDisabled = currentPage === totalPages ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="#" onclick="loadTemplates(${currentPage + 1})">下一页</a>
                </li>
            `);
        }

        // 搜索模板
        function searchTemplates() {
            loadTemplates(1);
        }

        // 重置搜索
        function resetSearch() {
            $('#searchForm')[0].reset();
            loadTemplates(1);
        }

        // 打开创建模态框
        function openCreateModal() {
            isEditMode = false;
            $('#templateModalLabel').text('新增模板');
            $('#templateForm')[0].reset();
            $('#templateId').val('');
            $('#fileRequired').show();
            $('#templateFile').prop('required', true);
            $('#fileInfo').hide();
        }

        // 编辑模板
        function editTemplate(templateId) {
            isEditMode = true;
            $('#templateModalLabel').text('编辑模板');
            $('#fileRequired').hide();
            $('#templateFile').prop('required', false);

            // 获取模板详情
            $.ajax({
                url: 'admin-api/excel/template/detail',
                method: 'GET',
                data: { templateId: templateId },
                success: function(response) {
                    if (response.code === 0) {
                        const template = response.data;
                        $('#templateId').val(template.templateId);
                        $('#templateName').val(template.templateName);
                        $('#templateType').val(template.templateType);
                        $('#description').val(template.description);
                        $('#headerRows').val(template.headerRows);
                        $('#dataStartRow').val(template.dataStartRow);
                        $('#headerStructure').val(template.headerStructure);

                        $('#templateModal').modal('show');
                    } else {
                        showAlert('获取模板详情失败：' + response.msg, 'danger');
                    }
                },
                error: function() {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            });
        }

        // 查看模板详情
        function viewTemplate(templateId) {
            $.ajax({
                url: 'admin-api/excel/template/detail',
                method: 'GET',
                data: { templateId: templateId },
                success: function(response) {
                    if (response.code === 0) {
                        const template = response.data;
                        let detailHtml = `
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>模板ID：</strong>${template.templateId}</p>
                                    <p><strong>模板名称：</strong>${template.templateName}</p>
                                    <p><strong>模板类型：</strong>${templateTypeMap[template.templateType]}</p>
                                    <p><strong>表头行数：</strong>${template.headerRows}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>数据起始行：</strong>${template.dataStartRow}</p>
                                    <p><strong>创建时间：</strong>${formatDateTime(template.createTime)}</p>
                                    <p><strong>更新时间：</strong>${formatDateTime(template.updateTime)}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>模板描述：</strong></p>
                                    <p class="text-muted">${template.description || '无描述'}</p>
                                </div>
                            </div>
                        `;

                        if (template.columns && template.columns.length > 0) {
                            detailHtml += `
                                <div class="row">
                                    <div class="col-12">
                                        <h6>列信息：</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>列名</th>
                                                        <th>字段名</th>
                                                        <th>字段类型</th>
                                                        <th>列位置</th>
                                                        <th>行位置</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                            `;

                            template.columns.forEach(column => {
                                detailHtml += `
                                    <tr>
                                        <td>${column.columnName}</td>
                                        <td>${column.columnField}</td>
                                        <td>${column.columnFieldType}</td>
                                        <td>${column.columnIndex}</td>
                                        <td>${column.rowIndex}</td>
                                    </tr>
                                `;
                            });

                            detailHtml += `
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }

                        showModal('模板详情', detailHtml);
                    } else {
                        showAlert('获取模板详情失败：' + response.msg, 'danger');
                    }
                },
                error: function() {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            });
        }

        // 删除模板
        function deleteTemplate(templateId, templateName) {
            if (confirm(`确定要删除模板"${templateName}"吗？此操作不可恢复！`)) {
                $.ajax({
                    url: 'admin-api/excel/template/delete',
                    method: 'DELETE',
                    data: { templateId: templateId },
                    success: function(response) {
                        if (response.code === 0) {
                            showAlert('删除成功', 'success');
                            loadTemplates(currentPage);
                        } else {
                            showAlert('删除失败：' + response.msg, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('网络错误，请稍后重试', 'danger');
                    }
                });
            }
        }

        // 下载模板
        function downloadTemplate(templateId) {
            window.open(`admin-api/excel/template/downloadTemplate?templateId=${templateId}`, '_blank');
        }

        // 保存模板
        function saveTemplate() {
            const form = $('#templateForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            const url = isEditMode ? 'admin-api/excel/template/update' : 'admin-api/excel/template/create';
            const method = isEditMode ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                method: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 0) {
                        showAlert(isEditMode ? '更新成功' : '创建成功', 'success');
                        $('#templateModal').modal('hide');
                        loadTemplates(currentPage);
                    } else {
                        showAlert((isEditMode ? '更新' : '创建') + '失败：' + response.msg, 'danger');
                    }
                },
                error: function() {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            });
        }

        // 初始化文件上传
        function initFileUpload() {
            const fileInput = $('#templateFile');
            const fileUploadArea = $('.file-upload-area');
            const fileInfo = $('#fileInfo');
            const fileName = $('#fileName');

            // 文件选择事件
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    fileName.text(file.name);
                    fileInfo.show();
                } else {
                    fileInfo.hide();
                }
            });

            // 拖拽事件
            fileUploadArea.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileUploadArea.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileUploadArea.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                        fileInput[0].files = files;
                        fileName.text(file.name);
                        fileInfo.show();
                    } else {
                        showAlert('请选择 Excel 文件（.xlsx 或 .xls）', 'warning');
                    }
                }
            });
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // 移除现有的提示
            $('.alert').remove();

            // 添加新提示到页面顶部
            $('body').prepend(alertHtml);

            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 3000);
        }

        // 显示模态框
        function showModal(title, content) {
            const modalHtml = `
                <div class="modal fade" id="detailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除现有的详情模态框
            $('#detailModal').remove();

            // 添加新的模态框
            $('body').append(modalHtml);
            $('#detailModal').modal('show');

            // 模态框关闭后移除
            $('#detailModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // ========== 列管理功能 ==========

        // 打开列管理模态框
        function manageColumns(templateId, templateName) {
            console.log('打开列管理模态框，模板ID:', templateId, '模板名称:', templateName);

            $('#currentTemplateId').val(templateId);
            $('#columnManagementModalLabel').html(`<i class="fas fa-columns"></i> 列信息管理 - ${templateName}`);

            // 重置状态
            currentColumns = [];
            selectedColumnIndex = -1;

            // 显示模态框
            $('#columnManagementModal').modal('show');

            // 模态框显示后加载数据
            $('#columnManagementModal').on('shown.bs.modal', function() {
                console.log('模态框已显示，开始加载列信息');
                loadTemplateColumns(templateId);
            });
        }

        // 加载模板列信息
        function loadTemplateColumns(templateId) {
            $.ajax({
                url: 'admin-api/excel/template/column/list',
                method: 'GET',
                data: { templateId: templateId },
                success: function(response) {
                    console.log('加载列信息响应:', response);
                    if (response.code === 0) {
                        // 确保 currentColumns 是数组
                        currentColumns = Array.isArray(response.data) ? response.data : [];
                        console.log('当前列信息:', currentColumns);
                        initColumnSpreadsheet();
                        renderColumnList();
                        renderHeaderPreview();
                    } else {
                        showAlert('加载列信息失败：' + response.msg, 'danger');
                        currentColumns = [];
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载列信息失败:', error);
                    showAlert('网络错误，请稍后重试', 'danger');
                    currentColumns = [];
                }
            });
        }

        // 初始化表格编辑器
        function initColumnSpreadsheet() {
            const container = document.getElementById('columnSpreadsheet');
            if (!container) {
                console.error('找不到表格容器元素');
                return;
            }

            container.innerHTML = '';

            // 确保 currentColumns 是数组
            if (!Array.isArray(currentColumns)) {
                console.warn('currentColumns 不是数组，重置为空数组');
                currentColumns = [];
            }

            console.log('初始化表格，列数据:', currentColumns);

            // 准备表格数据
            const headers = ['列名称', '字段名', '字段类型', '列位置', '行位置', '列跨度', '行跨度', '上级表头ID', '默认值', '是否隐藏'];
            const rows = currentColumns.map(col => [
                col.columnName || '',
                col.columnField || '',
                col.columnFieldType || 'String',
                col.columnIndex !== undefined ? col.columnIndex : 0,
                col.rowIndex !== undefined ? col.rowIndex : 0,
                col.columnSpan || 1,
                col.rowSpan || 1,
                col.parentColumnId || '',
                col.columnDefaultValue || '',
                col.columnHidden ? '是' : '否'
            ]);

            // 添加空行用于新增
            for (let i = 0; i < 5; i++) {
                rows.push(['', '', 'String', 0, 0, 1, 1, '', '', '否']);
            }

            // 构建符合 x-spreadsheet 格式的数据
            const spreadsheetData = [
                headers, // 表头行
                ...rows  // 数据行
            ];

            console.log('表格数据:', spreadsheetData);

            const data = {
                rows: {},
                cols: {},
                validations: {},
                styles: {},
                merges: []
            };

            // 设置所有行数据
            spreadsheetData.forEach((row, rowIndex) => {
                data.rows[rowIndex] = { cells: {} };
                row.forEach((cell, colIndex) => {
                    data.rows[rowIndex].cells[colIndex] = {
                        text: cell !== null && cell !== undefined ? cell.toString() : ''
                    };
                });
            });

            // 设置表头样式
            data.styles[0] = {
                bgcolor: '#f8f9fa',
                color: '#495057',
                bold: true
            };

            // 为表头行设置样式
            if (data.rows[0] && data.rows[0].cells) {
                Object.keys(data.rows[0].cells).forEach(colIndex => {
                    data.rows[0].cells[colIndex].style = 0;
                });
            }

            // 设置列宽
            data.cols = {
                0: { width: 120 }, // 列名称
                1: { width: 120 }, // 字段名
                2: { width: 100 }, // 字段类型
                3: { width: 80 },  // 列位置
                4: { width: 80 },  // 行位置
                5: { width: 80 },  // 列跨度
                6: { width: 80 },  // 行跨度
                7: { width: 100 }, // 上级表头ID
                8: { width: 120 }, // 默认值
                9: { width: 80 }   // 是否隐藏
            };

            // 检查 x_spreadsheet 是否可用
            if (typeof x_spreadsheet === 'undefined') {
                console.error('x_spreadsheet 未加载，使用备用表格');
                initFallbackTable(container, headers, rows);
                return;
            }

            try {
                // 创建表格
                columnSpreadsheet = x_spreadsheet(container, {
                    mode: 'edit',
                    showToolbar: false,
                    showGrid: true,
                    showContextmenu: true,
                    view: {
                        height: () => 450,
                        width: () => container.clientWidth - 20
                    }
                });

                columnSpreadsheet.loadData(data);

                // 监听数据变化
                columnSpreadsheet.on('cell-edited', (ri, ci, text, oldText) => {
                    console.log('Cell edited:', ri, ci, text, oldText);
                });

                console.log('表格初始化成功');
            } catch (error) {
                console.error('表格初始化失败:', error);
                console.log('使用备用表格');
                initFallbackTable(container, headers, rows);
            }
        }

        // 简化表格初始化
        function initFallbackTable(container, headers, rows) {
            const tableHeaders = ['选择', '列名称', '字段名', '字段类型', '列位置', '行位置', '列跨度', '行跨度', '上级表头ID', '默认值', '是否隐藏', '操作'];
            
            let tableHtml = '<div class="table-responsive">';
            tableHtml += '<table class="table table-bordered table-hover" id="columnTable">';
            
            // 表头
            tableHtml += '<thead class="table-dark"><tr>';
            tableHeaders.forEach(header => {
                tableHtml += `<th class="text-center">${header}</th>`;
            });
            tableHtml += '</tr></thead>';
            
            // 数据行
            tableHtml += '<tbody id="columnTableBody">';
            tableHtml += '</tbody></table>';
            tableHtml += '</div>';
            
            container.innerHTML = tableHtml;
            
            // 渲染表格数据
            renderTableRows();
            
            // 设置表格对象
            columnSpreadsheet = {
                isSimpleTable: true,
                getData: function() {
                    return getTableData();
                }
            };
        }

        // 渲染表格行
        function renderTableRows() {
            const tbody = $('#columnTableBody');
            tbody.empty();

            // 渲染现有数据
            currentColumns.forEach((column, index) => {
                const row = createTableRow(column, index);
                tbody.append(row);
            });

            // 添加空行用于新增
            for (let i = 0; i < 3; i++) {
                const emptyColumn = {
                    columnId: null,
                    columnName: '',
                    columnField: '',
                    columnFieldType: 'String',
                    columnIndex: currentColumns.length + i,
                    rowIndex: 0,
                    columnSpan: 1,
                    rowSpan: 1,
                    parentColumnId: null,
                    columnDefaultValue: '',
                    columnHidden: false
                };
                const row = createTableRow(emptyColumn, currentColumns.length + i, true);
                tbody.append(row);
            }
        }

        // 创建表格行
        function createTableRow(column, index, isEmpty = false) {
            const rowClass = isEmpty ? 'table-light' : '';
            const checkboxId = `checkbox_${index}`;
            
            return `
                <tr class="${rowClass}" data-index="${index}">
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input row-checkbox" id="${checkboxId}" ${isEmpty ? 'disabled' : ''}>
                    </td>
                    <td>
                        <input type="text" class="form-control form-control-sm" value="${column.columnName || ''}" 
                               onchange="updateColumnData(${index}, 'columnName', this.value)" ${isEmpty ? 'placeholder="列名称"' : ''}>
                    </td>
                    <td>
                        <input type="text" class="form-control form-control-sm" value="${column.columnField || ''}" 
                               onchange="updateColumnData(${index}, 'columnField', this.value)" ${isEmpty ? 'placeholder="字段名"' : ''}>
                    </td>
                    <td>
                        <select class="form-select form-select-sm" onchange="updateColumnData(${index}, 'columnFieldType', this.value)">
                            <option value="String" ${column.columnFieldType === 'String' ? 'selected' : ''}>String</option>
                            <option value="Integer" ${column.columnFieldType === 'Integer' ? 'selected' : ''}>Integer</option>
                            <option value="Long" ${column.columnFieldType === 'Long' ? 'selected' : ''}>Long</option>
                            <option value="Double" ${column.columnFieldType === 'Double' ? 'selected' : ''}>Double</option>
                            <option value="Boolean" ${column.columnFieldType === 'Boolean' ? 'selected' : ''}>Boolean</option>
                            <option value="Date" ${column.columnFieldType === 'Date' ? 'selected' : ''}>Date</option>
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm" value="${column.columnIndex !== undefined ? column.columnIndex : ''}" 
                               min="0" onchange="updateColumnData(${index}, 'columnIndex', parseInt(this.value))">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm" value="${column.rowIndex !== undefined ? column.rowIndex : ''}" 
                               min="0" onchange="updateColumnData(${index}, 'rowIndex', parseInt(this.value))">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm" value="${column.columnSpan || 1}" 
                               min="1" onchange="updateColumnData(${index}, 'columnSpan', parseInt(this.value))">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm" value="${column.rowSpan || 1}" 
                               min="1" onchange="updateColumnData(${index}, 'rowSpan', parseInt(this.value))">
                    </td>
                    <td>
                        <input type="text" class="form-control form-control-sm" value="${column.parentColumnId || ''}" 
                               onchange="updateColumnData(${index}, 'parentColumnId', this.value ? parseInt(this.value) : null)">
                    </td>
                    <td>
                        <input type="text" class="form-control form-control-sm" value="${column.columnDefaultValue || ''}" 
                               onchange="updateColumnData(${index}, 'columnDefaultValue', this.value)">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input" ${column.columnHidden ? 'checked' : ''} 
                               onchange="updateColumnData(${index}, 'columnHidden', this.checked)">
                    </td>
                    <td class="text-center">
                        ${isEmpty ? 
                            `<button type="button" class="btn btn-success btn-sm" onclick="addNewColumn(${index})">
                                <i class="fas fa-plus"></i>
                            </button>` :
                            `<button type="button" class="btn btn-danger btn-sm" onclick="deleteColumn(${index})">
                                <i class="fas fa-trash"></i>
                            </button>`
                        }
                    </td>
                </tr>
            `;
        }

        // 更新列数据
        function updateColumnData(index, field, value) {
            if (index >= currentColumns.length) {
                // 如果是空行，先创建新列
                const newColumn = {
                    columnId: null,
                    columnName: '',
                    columnField: '',
                    columnFieldType: 'String',
                    columnIndex: index,
                    rowIndex: 0,
                    columnSpan: 1,
                    rowSpan: 1,
                    parentColumnId: null,
                    columnDefaultValue: '',
                    columnHidden: false
                };
                currentColumns.push(newColumn);
            }
            
            currentColumns[index][field] = value;
            console.log(`更新列 ${index} 的 ${field} 为:`, value);
        }

        // 添加新列
        function addNewColumn(index) {
            const row = $(`tr[data-index="${index}"]`);
            const columnName = row.find('input[onchange*="columnName"]').val().trim();
            const columnField = row.find('input[onchange*="columnField"]').val().trim();
            
            if (!columnName || !columnField) {
                showAlert('请填写列名称和字段名', 'warning');
                return;
            }
            
            // 刷新表格显示
            renderTableRows();
            showAlert('添加成功', 'success');
        }

        // 删除列
        function deleteColumn(index) {
            if (confirm('确定要删除这一列吗？')) {
                currentColumns.splice(index, 1);
                renderTableRows();
                showAlert('删除成功', 'success');
            }
        }

        // 获取表格数据
        function getTableData() {
            const rows = {};
            currentColumns.forEach((column, index) => {
                const rowIndex = index + 1; // 跳过表头
                rows[rowIndex] = {
                    cells: {
                        0: { text: column.columnName || '' },
                        1: { text: column.columnField || '' },
                        2: { text: column.columnFieldType || 'String' },
                        3: { text: (column.columnIndex !== undefined ? column.columnIndex : 0).toString() },
                        4: { text: (column.rowIndex !== undefined ? column.rowIndex : 0).toString() },
                        5: { text: (column.columnSpan || 1).toString() },
                        6: { text: (column.rowSpan || 1).toString() },
                        7: { text: (column.parentColumnId || '').toString() },
                        8: { text: column.columnDefaultValue || '' },
                        9: { text: column.columnHidden ? '是' : '否' }
                    }
                };
            });
            return { rows };
        }

        // 渲染列列表
        function renderColumnList() {
            const columnList = $('#columnList');
            columnList.empty();

            console.log('渲染列列表，当前列数:', currentColumns.length);

            if (!Array.isArray(currentColumns) || currentColumns.length === 0) {
                columnList.append(`
                    <div class="list-group-item text-center text-muted">
                        暂无列信息
                    </div>
                `);
                return;
            }

            currentColumns.forEach((column, index) => {
                const columnName = column.columnName || '未命名列';
                const columnField = column.columnField || '未设置';
                const columnFieldType = column.columnFieldType || 'String';
                const columnIndex = column.columnIndex !== undefined ? column.columnIndex : 0;
                const rowIndex = column.rowIndex !== undefined ? column.rowIndex : 0;

                const item = `
                    <div class="list-group-item list-group-item-action ${index === selectedColumnIndex ? 'active' : ''}"
                         onclick="selectColumn(${index})">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${columnName}</h6>
                            <small class="field-type-badge badge bg-secondary">${columnFieldType}</small>
                        </div>
                        <p class="mb-1">${columnField}</p>
                        <small>位置: (${columnIndex}, ${rowIndex})</small>
                    </div>
                `;
                columnList.append(item);
            });
        }

        // 选择列
        function selectColumn(index) {
            selectedColumnIndex = index;
            renderColumnList();

            const column = currentColumns[index];
            if (column) {
                $('#editColumnId').val(column.columnId || '');
                $('#editColumnName').val(column.columnName || '');
                $('#editColumnField').val(column.columnField || '');
                $('#editColumnFieldType').val(column.columnFieldType || '');
                $('#editColumnIndex').val(column.columnIndex || 0);
                $('#editRowIndex').val(column.rowIndex || 0);
                $('#editColumnSpan').val(column.columnSpan || 1);
                $('#editRowSpan').val(column.rowSpan || 1);
                $('#editParentColumnId').val(column.parentColumnId || '');
                $('#editColumnDefaultValue').val(column.columnDefaultValue || '');
                $('#editColumnHidden').prop('checked', column.columnHidden || false);

                // 更新上级表头选项
                updateParentColumnOptions();
            }
        }

        // 更新上级表头选项
        function updateParentColumnOptions() {
            const select = $('#editParentColumnId');
            const currentColumnId = $('#editColumnId').val();

            select.find('option:not(:first)').remove();

            currentColumns.forEach(column => {
                if (column.columnId && column.columnId != currentColumnId) {
                    select.append(`<option value="${column.columnId}">${column.columnName}</option>`);
                }
            });
        }

        // 添加新列
        function addColumn() {
            const nextIndex = getNextAvailablePosition();
            const newColumn = {
                columnId: null,
                columnName: '新列' + (currentColumns.length + 1),
                columnField: 'newField' + (currentColumns.length + 1),
                columnFieldType: 'String',
                columnIndex: nextIndex,
                rowIndex: 0,
                columnSpan: 1,
                rowSpan: 1,
                parentColumnId: null,
                columnDefaultValue: '',
                columnHidden: false
            };

            currentColumns.push(newColumn);
            initColumnSpreadsheet();
            renderColumnList();
            selectColumn(currentColumns.length - 1);
        }

        // 删除选中的列
        function deleteSelectedColumns() {
            if (selectedColumnIndex >= 0 && selectedColumnIndex < currentColumns.length) {
                if (confirm('确定要删除选中的列吗？')) {
                    currentColumns.splice(selectedColumnIndex, 1);
                    selectedColumnIndex = -1;
                    initColumnSpreadsheet();
                    renderColumnList();
                    clearColumnForm();
                }
            } else {
                showAlert('请先选择要删除的列', 'warning');
            }
        }

        // 保存列详情
        function saveColumnDetail() {
            const form = $('#columnDetailForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const columnData = {
                columnId: $('#editColumnId').val() || null,
                columnName: $('#editColumnName').val(),
                columnField: $('#editColumnField').val(),
                columnFieldType: $('#editColumnFieldType').val(),
                columnIndex: parseInt($('#editColumnIndex').val()),
                rowIndex: parseInt($('#editRowIndex').val()),
                columnSpan: parseInt($('#editColumnSpan').val()) || 1,
                rowSpan: parseInt($('#editRowSpan').val()) || 1,
                parentColumnId: $('#editParentColumnId').val() || null,
                columnDefaultValue: $('#editColumnDefaultValue').val(),
                columnHidden: $('#editColumnHidden').prop('checked')
            };

            // 验证列位置是否重复
            const duplicateIndex = currentColumns.findIndex((col, index) =>
                index !== selectedColumnIndex &&
                col.columnIndex === columnData.columnIndex &&
                col.rowIndex === columnData.rowIndex
            );

            if (duplicateIndex >= 0) {
                showAlert('列位置不能重复', 'warning');
                return;
            }

            if (selectedColumnIndex >= 0) {
                currentColumns[selectedColumnIndex] = columnData;
            } else {
                currentColumns.push(columnData);
                selectedColumnIndex = currentColumns.length - 1;
            }

            initColumnSpreadsheet();
            renderColumnList();
            renderHeaderPreview();
            showAlert('保存成功', 'success');
        }

        // 清空表单
        function clearColumnForm() {
            $('#columnDetailForm')[0].reset();
            $('#editColumnId').val('');
            selectedColumnIndex = -1;
            renderColumnList();
        }

        // 保存所有列信息
        function saveAllColumns() {
            const templateId = $('#currentTemplateId').val();
            if (!templateId) {
                showAlert('模板ID不能为空', 'danger');
                return;
            }

            if (!columnSpreadsheet) {
                showAlert('表格未初始化', 'danger');
                return;
            }

            if (columnSpreadsheet.isFallback) {
                showAlert('当前使用简化表格模式，请在表单编辑模式中进行编辑和保存', 'warning');
                $('#form-tab').click(); // 切换到表单编辑模式
                return;
            }

            try {
                // 从表格中获取最新数据
                const spreadsheetData = columnSpreadsheet.getData();
                console.log('表格数据:', spreadsheetData);

                const updatedColumns = [];

                // 解析表格数据
                if (spreadsheetData.rows) {
                    Object.keys(spreadsheetData.rows).forEach(rowIndex => {
                        const rowNum = parseInt(rowIndex);
                        if (rowNum === 0) return; // 跳过表头

                        const row = spreadsheetData.rows[rowIndex];
                        if (!row || !row.cells) return;

                        const columnName = row.cells[0]?.text?.trim();
                        const columnField = row.cells[1]?.text?.trim();

                        if (columnName && columnField) {
                            const originalIndex = rowNum - 1; // 原始数据索引
                            const originalColumn = currentColumns[originalIndex] || {};

                            const columnData = {
                                columnId: originalColumn.columnId || null,
                                columnName: columnName,
                                columnField: columnField,
                                columnFieldType: row.cells[2]?.text?.trim() || 'String',
                                columnIndex: parseInt(row.cells[3]?.text) || 0,
                                rowIndex: parseInt(row.cells[4]?.text) || 0,
                                columnSpan: parseInt(row.cells[5]?.text) || 1,
                                rowSpan: parseInt(row.cells[6]?.text) || 1,
                                parentColumnId: row.cells[7]?.text?.trim() ? parseInt(row.cells[7].text.trim()) : null,
                                columnDefaultValue: row.cells[8]?.text?.trim() || '',
                                columnHidden: row.cells[9]?.text?.trim() === '是'
                            };
                            updatedColumns.push(columnData);
                        }
                    });
                }

                console.log('解析的列数据:', updatedColumns);

                const requestData = {
                    templateId: parseInt(templateId),
                    columns: updatedColumns
                };

                $.ajax({
                    url: 'admin-api/excel/template/column/batch-save',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    success: function(response) {
                        if (response.code === 0) {
                            showAlert('保存成功', 'success');
                            loadTemplateColumns(templateId);
                        } else {
                            showAlert('保存失败：' + response.msg, 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('保存失败:', error);
                        showAlert('网络错误，请稍后重试', 'danger');
                    }
                });
            } catch (error) {
                console.error('保存数据处理失败:', error);
                showAlert('数据处理失败: ' + error.message, 'danger');
            }
        }

        // 渲染表头预览
        function renderHeaderPreview() {
            const preview = $('#headerPreview');
            preview.empty();

            if (!Array.isArray(currentColumns) || currentColumns.length === 0) {
                preview.append('<div class="text-center text-muted p-4">暂无列信息</div>');
                return;
            }

            try {
                // 按行和列排序
                const sortedColumns = [...currentColumns].sort((a, b) => {
                    const aRowIndex = a.rowIndex !== undefined ? a.rowIndex : 0;
                    const bRowIndex = b.rowIndex !== undefined ? b.rowIndex : 0;
                    const aColumnIndex = a.columnIndex !== undefined ? a.columnIndex : 0;
                    const bColumnIndex = b.columnIndex !== undefined ? b.columnIndex : 0;

                    if (aRowIndex !== bRowIndex) {
                        return aRowIndex - bRowIndex;
                    }
                    return aColumnIndex - bColumnIndex;
                });

                // 计算表头结构
                const maxRow = Math.max(...currentColumns.map(col => (col.rowIndex !== undefined ? col.rowIndex : 0)), 0) + 1;
                const maxCol = Math.max(...currentColumns.map(col => {
                    const colIndex = col.columnIndex !== undefined ? col.columnIndex : 0;
                    const colSpan = col.columnSpan || 1;
                    return colIndex + colSpan - 1;
                }), 0) + 1;

            // 创建二维数组来表示表格
            const grid = Array(maxRow).fill(null).map(() => Array(maxCol).fill(null));

                // 填充表格数据
                sortedColumns.forEach(column => {
                    const rowIndex = column.rowIndex !== undefined ? column.rowIndex : 0;
                    const columnIndex = column.columnIndex !== undefined ? column.columnIndex : 0;
                    const rowSpan = column.rowSpan || 1;
                    const colSpan = column.columnSpan || 1;

                    for (let r = rowIndex; r < rowIndex + rowSpan; r++) {
                        for (let c = columnIndex; c < columnIndex + colSpan; c++) {
                            if (r < maxRow && c < maxCol) {
                                grid[r][c] = {
                                    column: column,
                                    isMain: r === rowIndex && c === columnIndex,
                                    rowSpan: rowSpan,
                                    colSpan: colSpan
                                };
                            }
                        }
                    }
                });

            // 生成HTML表格
            let tableHtml = '<table class="table table-bordered table-sm">';

            for (let row = 0; row < maxRow; row++) {
                tableHtml += '<tr>';
                for (let col = 0; col < maxCol; col++) {
                    const cell = grid[row][col];

                    if (cell && cell.isMain) {
                        const colspan = cell.colSpan > 1 ? ` colspan="${cell.colSpan}"` : '';
                        const rowspan = cell.rowSpan > 1 ? ` rowspan="${cell.rowSpan}"` : '';
                        const hiddenClass = cell.column.columnHidden ? ' table-secondary' : '';
                        const fieldTypeClass = getFieldTypeClass(cell.column.columnFieldType);

                        tableHtml += `<th${colspan}${rowspan} class="text-center${hiddenClass}">
                            <div>${cell.column.columnName}</div>
                            <small class="badge ${fieldTypeClass}">${cell.column.columnFieldType}</small>
                            <br><small class="text-muted">${cell.column.columnField}</small>
                        </th>`;
                    } else if (!cell) {
                        tableHtml += '<td class="text-center text-muted bg-light">-</td>';
                    }
                    // 如果是被合并的单元格，跳过
                }
                tableHtml += '</tr>';
            }

            tableHtml += '</table>';

                // 添加统计信息
                const stats = `
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <strong>${currentColumns.length}</strong><br>
                                <small class="text-muted">总列数</small>
                            </div>
                            <div class="col-md-3">
                                <strong>${maxRow}</strong><br>
                                <small class="text-muted">表头行数</small>
                            </div>
                            <div class="col-md-3">
                                <strong>${maxCol}</strong><br>
                                <small class="text-muted">表格列数</small>
                            </div>
                            <div class="col-md-3">
                                <strong>${currentColumns.filter(col => col.columnHidden).length}</strong><br>
                                <small class="text-muted">隐藏列数</small>
                            </div>
                        </div>
                    </div>
                `;

                preview.append(tableHtml + stats);
            } catch (error) {
                console.error('渲染表头预览失败:', error);
                preview.append('<div class="alert alert-danger">预览生成失败: ' + error.message + '</div>');
            }
        }

        // 获取字段类型对应的样式类
        function getFieldTypeClass(fieldType) {
            const typeClassMap = {
                'String': 'bg-primary',
                'Integer': 'bg-success',
                'Long': 'bg-success',
                'Double': 'bg-warning',
                'Boolean': 'bg-info',
                'Date': 'bg-secondary'
            };
            return typeClassMap[fieldType] || 'bg-dark';
        }

        // 预览表头
        function previewHeader() {
            renderHeaderPreview();
            $('#preview-tab').click();
        }

        // 导入列信息
        function importColumns() {
            $('#importColumnsModal').modal('show');
        }

        // 处理导入
        function processImport() {
            const file = $('#importFile')[0].files[0];
            if (!file) {
                showAlert('请选择要导入的文件', 'warning');
                return;
            }

            const templateId = $('#currentTemplateId').val();
            const overwrite = $('#overwriteExisting').prop('checked');

            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file);
            formData.append('templateId', templateId);
            formData.append('overwrite', overwrite);

            $.ajax({
                url: 'admin-api/excel/template/column/import',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 0) {
                        showAlert('导入成功', 'success');
                        $('#importColumnsModal').modal('hide');
                        loadTemplateColumns(templateId);
                    } else {
                        showAlert('导入失败：' + response.msg, 'danger');
                    }
                },
                error: function() {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            });
        }

        // 导出列信息
        function exportColumns() {
            const templateId = $('#currentTemplateId').val();
            if (!templateId) {
                showAlert('模板ID不能为空', 'danger');
                return;
            }

            window.open(`admin-api/excel/template/column/export?templateId=${templateId}`, '_blank');
        }

        // 验证字段名格式
        function validateFieldName(fieldName) {
            const pattern = /^[a-zA-Z][a-zA-Z0-9_]*$/;
            return pattern.test(fieldName);
        }

        // 验证列位置是否重复
        function validateColumnPosition(columnIndex, rowIndex, excludeIndex = -1) {
            return !currentColumns.some((col, index) =>
                index !== excludeIndex &&
                col.columnIndex === columnIndex &&
                col.rowIndex === rowIndex
            );
        }

        // 获取下一个可用的列位置
        function getNextAvailablePosition() {
            if (!Array.isArray(currentColumns) || currentColumns.length === 0) {
                return 0;
            }
            const maxIndex = Math.max(...currentColumns.map(col => col.columnIndex !== undefined ? col.columnIndex : 0), -1);
            return maxIndex + 1;
        }

        // 初始化列管理模态框事件
        function initColumnManagementEvents() {
            // 字段名输入验证
            $('#editColumnField').on('blur', function() {
                const fieldName = $(this).val();
                if (fieldName && !validateFieldName(fieldName)) {
                    showAlert('字段名格式不正确，应以字母开头，只能包含字母、数字和下划线', 'warning');
                    $(this).focus();
                }
            });

            // 列位置输入验证
            $('#editColumnIndex, #editRowIndex').on('blur', function() {
                const columnIndex = parseInt($('#editColumnIndex').val());
                const rowIndex = parseInt($('#editRowIndex').val());

                if (!isNaN(columnIndex) && !isNaN(rowIndex)) {
                    if (!validateColumnPosition(columnIndex, rowIndex, selectedColumnIndex)) {
                        showAlert('该位置已被其他列占用', 'warning');
                        $(this).focus();
                    }
                }
            });

            // 模态框关闭时清理数据
            $('#columnManagementModal').on('hidden.bs.modal', function() {
                currentColumns = [];
                selectedColumnIndex = -1;
                if (columnSpreadsheet) {
                    columnSpreadsheet = null;
                }
            });
        }

        // 调试列数据
        function debugColumnData() {
            console.log('=== 调试信息 ===');
            console.log('当前模板ID:', $('#currentTemplateId').val());
            console.log('当前列数据:', currentColumns);
            console.log('选中列索引:', selectedColumnIndex);
            console.log('表格对象:', columnSpreadsheet);

            if (columnSpreadsheet && !columnSpreadsheet.isFallback) {
                console.log('表格数据:', columnSpreadsheet.getData());
            }

            showAlert('调试信息已输出到控制台', 'info');
        }

        // 页面加载完成后初始化事件
        $(document).ready(function() {
            loadTemplates();
            initFileUpload();
            initColumnManagementEvents();
        });
    </script>
</body>
</html>
