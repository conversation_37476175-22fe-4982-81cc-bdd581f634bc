package cn.savas.hub.module.excel.enums;

import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * excel 错误码枚举类
 *
 * excel 系统，使用 1-023-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== Excel 模板相关 1-023-000-000 ==========
    /**
     * 模板不存在
     */
    ErrorCode EXCEL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_023_000_001, "Excel模板不存在");

    /**
     * 模板下载失败
     */
    ErrorCode EXCEL_TEMPLATE_DOWNLOAD_FAILED = new ErrorCode(1_023_000_002, "Excel模板下载失败");

    /**
     * 模板名称已存在
     */
    ErrorCode EXCEL_TEMPLATE_NAME_EXISTS = new ErrorCode(1_023_000_003, "Excel模板名称已存在");

    /**
     * 模板文件上传失败
     */
    ErrorCode EXCEL_TEMPLATE_UPLOAD_FAILED = new ErrorCode(1_023_000_004, "Excel模板文件上传失败");

    /**
     * 模板文件格式不正确
     */
    ErrorCode EXCEL_TEMPLATE_FILE_FORMAT_ERROR = new ErrorCode(1_023_000_005, "Excel模板文件格式不正确");

    /**
     * 模板删除失败，存在关联数据
     */
    ErrorCode EXCEL_TEMPLATE_DELETE_FAILED_HAS_DATA = new ErrorCode(1_023_000_006, "Excel模板删除失败，存在关联数据");

    /**
     * 模板导出失败
     */
    ErrorCode EXCEL_TEMPLATE_EXPORT_FAILED = new ErrorCode(1_023_000_007, "Excel模板导出失败");

    // ========== Excel 列信息相关 1-023-001-000 ==========
    /**
     * 列信息不存在
     */
    ErrorCode EXCEL_COLUMN_NOT_EXISTS = new ErrorCode(1_023_001_001, "Excel列信息不存在");

    /**
     * 列位置已存在
     */
    ErrorCode EXCEL_COLUMN_POSITION_EXISTS = new ErrorCode(1_023_001_002, "Excel列位置已存在");

    /**
     * 字段名已存在
     */
    ErrorCode EXCEL_COLUMN_FIELD_EXISTS = new ErrorCode(1_023_001_003, "Excel字段名已存在");

    /**
     * 列存在子列，无法删除
     */
    ErrorCode EXCEL_COLUMN_HAS_CHILDREN = new ErrorCode(1_023_001_004, "Excel列存在子列，无法删除");
}
