package cn.savas.hub.module.excel.api.template.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/25 10:11
 */
@Data
public class ExcelColumnDTO {
    /**
     * 列唯一标识
     */
    private Long columnId;
    private Long templateId;
    /**
     * Excel 中的原始列头名称，如“销售额”
     */
    private String columnName;
    /**
     * 映射字段名，如“sales_amount”
     */
    private String columnField;
    private String columnFieldType;
    /**
     * 列在 Excel 中的位置（从 0 开始）
     */
    private Integer columnIndex;
    /**
     * 表头所在的行号（支持多行表头）
     */
    private Integer rowIndex;
    /**
     * 列合并的跨度
     */
    private Integer columnSpan;
    /**
     * 行合并的跨度
     */
    private Integer rowSpan;
    /**
     * 上级表头ID（用于嵌套关系，可选）
     */
    private Long parentColumnId;
}
