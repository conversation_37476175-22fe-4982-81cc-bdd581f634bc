package cn.savas.hub.module.excel.api.template;

import cn.savas.hub.module.excel.api.template.dto.ExcelColumnDTO;
import cn.savas.hub.module.excel.api.template.dto.ExcelTemplateDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/23 18:35
 */
public interface ExcelTemplatesApi {
    /**
     * 根据Excel文件和模板ID解析Excel
     */
    List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException;

    /**
     * 根据模板ID获取Excel模板
     */
    List<ExcelColumnDTO> getTemplateColumns(Long templateId);

    /**
     * 根据模板名称获取Excel模板
     */
    List<ExcelColumnDTO> getTemplateColumnsByName(String templateName);

    ExcelTemplateDTO getTemplateByName(String templateName);

    /**
     * 根据模板类型获取Excel模板
     */
    List<ExcelTemplateDTO> getTemplateByType(Integer templateType);

    /**
     * 根据模板ID获取 Excel 模板
     */
    ExcelTemplateDTO getTemplateById(Long templateId);
}
