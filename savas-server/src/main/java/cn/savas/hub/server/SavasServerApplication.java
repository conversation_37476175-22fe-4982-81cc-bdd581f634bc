package cn.savas.hub.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 *
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${savas.info.base-package}
@SpringBootApplication(scanBasePackages = {"${savas.info.base-package}.server", "${savas.info.base-package}.module"})
public class SavasServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SavasServerApplication.class, args);
    }

}
