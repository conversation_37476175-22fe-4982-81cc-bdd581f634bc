package cn.savas.hub.algocell.service.engine;

import cn.savas.hub.module.algocell.service.engine.Trie;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Trie树测试类
 *
 * <AUTHOR>
 */
class TrieTest {

    private Trie trie;

    @BeforeEach
    void setUp() {
        trie = new Trie(false); // 不区分大小写
    }

    @Test
    void testInsertAndContains() {
        // 测试插入和查找
        trie.insert("阀门");
        trie.insert("Q345R");
        trie.insert("CL150");

        assertTrue(trie.contains("阀门"));
        assertTrue(trie.contains("Q345R"));
        assertTrue(trie.contains("CL150"));
        assertFalse(trie.contains("不存在的术语"));

        assertEquals(3, trie.getTermCount());
    }

    @Test
    void testCaseInsensitive() {
        // 测试大小写不敏感
        trie.insert("Q345R");

        assertTrue(trie.contains("Q345R"));
        assertTrue(trie.contains("q345r"));
        assertTrue(trie.contains("Q345r"));
    }

    @Test
    void testFindAll() {
        // 测试查找所有匹配
        trie.insert("阀门");
        trie.insert("Q345R");
        trie.insert("CL150");

        String text = "这是一个Q345R材质的阀门，压力等级为CL150";
        List<Trie.MatchResult> results = trie.findAll(text);

        assertEquals(3, results.size());

        // 验证匹配结果
        boolean foundQ345R = false, found阀门 = false, foundCL150 = false;
        for (Trie.MatchResult result : results) {
            if ("Q345R".equals(result.term)) {
                foundQ345R = true;
                assertEquals("Q345R", result.originalText);
            } else if ("阀门".equals(result.term)) {
                found阀门 = true;
                assertEquals("阀门", result.originalText);
            } else if ("CL150".equals(result.term)) {
                foundCL150 = true;
                assertEquals("CL150", result.originalText);
            }
        }

        assertTrue(foundQ345R);
        assertTrue(found阀门);
        assertTrue(foundCL150);
    }

    @Test
    void testOverlappingMatches() {
        // 测试重叠匹配
        trie.insert("阀");
        trie.insert("阀门");

        String text = "阀门";
        List<Trie.MatchResult> results = trie.findAll(text);

        assertEquals(2, results.size());

        // 应该同时匹配"阀"和"阀门"
        boolean found阀 = false, found阀门 = false;
        for (Trie.MatchResult result : results) {
            if ("阀".equals(result.term)) {
                found阀 = true;
            } else if ("阀门".equals(result.term)) {
                found阀门 = true;
            }
        }

        assertTrue(found阀);
        assertTrue(found阀门);
    }

    @Test
    void testEmptyAndNull() {
        // 测试空值和null
        trie.insert(null);
        trie.insert("");

        assertEquals(0, trie.getTermCount());

        assertFalse(trie.contains(null));
        assertFalse(trie.contains(""));

        List<Trie.MatchResult> results = trie.findAll(null);
        assertTrue(results.isEmpty());

        results = trie.findAll("");
        assertTrue(results.isEmpty());
    }

    @Test
    void testClear() {
        // 测试清空
        trie.insert("阀门");
        trie.insert("Q345R");

        assertEquals(2, trie.getTermCount());

        trie.clear();

        assertEquals(0, trie.getTermCount());
        assertFalse(trie.contains("阀门"));
        assertFalse(trie.contains("Q345R"));
    }

    @Test
    void testChineseAndEnglish() {
        // 测试中英文混合
        trie.insert("API 602");
        trie.insert("英寸直径");
        trie.insert("DN15");

        String text = "API 602标准的英寸直径DN15阀门";
        List<Trie.MatchResult> results = trie.findAll(text);

        assertEquals(3, results.size());
    }

    @Test
    void testSpecialCharacters() {
        // 测试特殊字符
        trie.insert("A105/13Cr*STL");
        trie.insert("OS&Y");

        String text = "材质A105/13Cr*STL，结构OS&Y";
        List<Trie.MatchResult> results = trie.findAll(text);

        assertEquals(2, results.size());
    }
}
