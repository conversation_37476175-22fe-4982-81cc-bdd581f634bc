package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/21 16:55
 */
@Getter
public enum BillProfessionEnum {

    /**
     * 安装
     */
    INSTALL("Installtion", "安装"),
    /**
     * 建筑
     */
    BUILD("Architecture", "建筑"),
    ;

    private final String code;
    private final String name;

    BillProfessionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


}
