package cn.savas.hub.framework.common.client.util;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:01
 */
public class ClientDateConvertUtil {
    // 1899-12-30 起始日期
    private static LocalDateTime BASE_DATE = LocalDateTime.of(1899, 12, 30, 0, 0, 0, 0);

    public static LocalDateTime convertDate(Float value) {
        // 如果值为 null，直接返回 null
        if (value == null) {
            return null;
        }
        // 获取整数部分，表示天数
        int days = value.intValue();

        // 获取小数部分，表示当天的时间（比例）
        float timeFraction = value - days;

        // 计算出日期：从 BASE_DATE 开始加上 days
        LocalDateTime date = BASE_DATE.plusDays(days);

        // 计算出当天的时间：小数部分转化为时间（秒）
        int secondsOfDay = (int) (timeFraction * 86400); // 86400 秒即一天的秒数

        // 计算当天的具体时间
        return date.plusSeconds(secondsOfDay);
    }
}
