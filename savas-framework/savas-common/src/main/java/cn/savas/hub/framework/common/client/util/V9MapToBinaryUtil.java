package cn.savas.hub.framework.common.client.util;

import cn.hutool.core.util.ZipUtil;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1.4个字节存贮个数
 * 2.4个字节存储key 长度
 * 3.key 长度*2 key值
 * 4.添加4个空白类型
 * 5.10个字节存储value 长度
 * 6.value 转成byte[]
 */

public class V9MapToBinaryUtil {

    public static byte[] convertToBinary(Map<String, BigDecimal> feeMap){
        if (feeMap == null || feeMap.isEmpty()) {
            return new byte[0];
        }
        List<byte[]> list = new ArrayList<>();
        //个数
        byte[] lengthByte = ByteConvertUtil.intToByte(feeMap.size());
        list.add(lengthByte);
        for (Map.Entry<String, BigDecimal> entry : feeMap.entrySet()) {
            //key 长度
            byte[] keyByte =ByteConvertUtil. intToByte(entry.getKey().length());
            list.add(keyByte);
            //key
            byte[] key = ByteConvertUtil.stringToByteAddWhitespace(entry.getKey());
            list.add(key);
            //数据类型//添加4位占位符
            list.add(new byte[]{0, 0, 0, 0});
            //value
            byte[] value = decimalToBytes(entry.getValue());
            list.add(value);
        }
        //合并
        byte[] all = ByteConvertUtil.combineArrays(list);
        //转码//根据实际情况处理
        byte[] bytes = ByteConvertUtil.convertToClient(all);
        //lib 压缩
        return ZipUtil.zlib(bytes, 3);
    }


    /**
     * 总长度10个字节
     *
     * @param decimal
     * @return
     */
    public static byte[] decimalToBytes(BigDecimal decimal) {
        byte[] bytes = new byte[10];
        BigDecimal value = decimal.abs();
        //指数位
        int exponent = Math.getExponent(value.doubleValue());
        //为0
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return new byte[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
        }
        String hexString = Integer.toHexString(exponent + 16383).toUpperCase();
        bytes[8] = (byte) Integer.parseInt(hexString.substring(2, 4), 16);
        bytes[9] |= (byte) Integer.parseInt(hexString.substring(0, 2), 16);
        BigInteger bigIntegerValue = BigInteger.ZERO;
        //获取尾数位 正常数 2.48
        if (exponent > 0) {
            BigDecimal mantissa = value.divide(BigDecimal.valueOf(2).pow(exponent), MathContext.DECIMAL128);
            bigIntegerValue = mantissa.multiply(BigDecimal.valueOf(Math.pow(2, 63))).toBigInteger();
        }
        //获取尾数位 0点几 0.48
        if (exponent < 0) {
            bigIntegerValue = value.multiply(BigDecimal.valueOf(Math.pow(2, 63 - exponent))).toBigInteger();
        }
        //获取尾数位 1点几 1.48
        if (exponent == 0) {
            bigIntegerValue = value.multiply(BigDecimal.valueOf(Math.pow(2, 63))).toBigInteger();
        }

        String hexCode = bigIntegerValue.toString(16).toUpperCase();
        // 循环遍历字符串，每两个字符转换为一个字节 反转,从右向左取
        for (int i = 0; i < hexCode.length() / 2; i++) {
            // 获取每两个字符
            int index = hexCode.length() - 2 * (i + 1);
            String byteString = hexCode.substring(index, index + 2);
            // 将十六进制字符串转换为字节
            bytes[i] = (byte) Integer.parseInt(byteString, 16);
        }
        //指数位
        boolean isNegative = decimal.signum() < 0;
        if (isNegative) {
            bytes[9] |= (byte) 0x80;
        }
        return bytes;
    }

}

