package cn.savas.hub.framework.common.util.number;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * 数字的工具类，补全 {@link cn.hutool.core.util.NumberUtil} 的功能
 *
 * <AUTHOR>
 */
public class NumberUtils {

    public static Long parseLong(String str) {
        return StrUtil.isNotEmpty(str) ? Long.valueOf(str) : null;
    }

    public static Integer parseInt(String str) {
        return StrUtil.isNotEmpty(str) ? Integer.valueOf(str) : null;
    }

    /**
     * 通过经纬度获取地球上两点之间的距离
     * <p>
     * 参考 <<a href="https://gitee.com/dromara/hutool/blob/1caabb586b1f95aec66a21d039c5695df5e0f4c1/hutool-core/src/main/java/cn/hutool/core/util/DistanceUtil.java">DistanceUtil</a>> 实现，目前它已经被 hutool 删除
     *
     * @param lat1 经度1
     * @param lng1 纬度1
     * @param lat2 经度2
     * @param lng2 纬度2
     * @return 距离，单位：千米
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = lat1 * Math.PI / 180.0;
        double radLat2 = lat2 * Math.PI / 180.0;
        double a = radLat1 - radLat2;
        double b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * 6378.137;
        distance = Math.round(distance * 10000d) / 10000d;
        return distance;
    }

    /**
     * 提供精确的乘法运算
     * <p>
     * 和 hutool {@link NumberUtil#mul(BigDecimal...)} 的差别是，如果存在 null，则返回 null
     *
     * @param values 多个被乘值
     * @return 积
     */
    public static BigDecimal mul(BigDecimal... values) {
        for (BigDecimal value : values) {
            if (value == null) {
                return null;
            }
        }
        return NumberUtil.mul(values);
    }

    /**
     * 如果为空，则返回0
     *
     * @param val
     * @return
     */
    public static Integer ifEmptyRetZero(Integer val) {
        return ifEmptyRetDefVal(val, 0);
    }

    /**
     * 如果为空，则返回默认值
     *
     * @param val    判断值
     * @param defVal 默认值
     * @param <T>    类型
     * @return
     */
    public static <T> T ifEmptyRetDefVal(T val, T defVal) {
        if (Objects.isNull(val)) {
            return defVal;
        }

        return val;
    }

    /**
     * 1.1，1.2 返回最后一位
     *
     * @param val
     * @return
     */
    public static Integer ifEmptyStringZero(String val) {
        if (StringUtils.isEmpty(val) || val.equals("")) {
            return 99;
        }
        String[] str = val.split("\\.");
        if (isNumeric(str[str.length - 1])) {
            return Integer.valueOf(str[str.length - 1]);
        }
        return 99;
    }

    /**
     * 判断是否为正整数
     *
     * @param string
     * @return
     */
    public static boolean isNumeric(String string) {
        Pattern pattern = compile("[0-9]*");
        return pattern.matcher(string).matches();
    }

    /**
     * 判断是否全部由数字和.组成
     * @param str
     * @return
     */
    public static boolean isNumber(String str) {
        String regex = "\\d*\\.?\\d+";
        return str.matches(regex);
    }

}
