package cn.savas.hub.framework.common.client.util;

import cn.hutool.core.io.FileUtil;
import cn.savas.hub.framework.common.util.io.FileUtils;
import cn.savas.hub.framework.common.util.io.IoUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.Inflater;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 解压/压缩客户端文件
 */
public class V9PackageUtil {

    private static final int BUFFER_SIZE = 1024;

    public static String unZipFileToDB(byte[] zipFileData) {
        return unZipFileToDB(zipFileData, null);
    }

    public static String unZipFileToDB(byte[] zipFileData, String outUri) {
        byte[] decompressedData = unzipWithoutHeader(zipFileData);
        // 生成db文件
        if (outUri != null) {
            unzipSqliteDBFile(decompressedData, outUri);
            return outUri;
        }
        String rootPath = System.getProperty("java.io.tmpdir");
        String filePath = rootPath + (FileUtils.generatePath(zipFileData, ".db"));
        unzipSqliteDBFile(decompressedData, filePath);
        return filePath;
    }

    /**
     * 无头解压缩(第一层解压)
     *
     * @param zipFileData
     * @return
     */
    private static byte[] unzipWithoutHeader(byte[] zipFileData) {
        // 解压缩
        Inflater inflater = new Inflater();
        inflater.setInput(zipFileData);
        byte[] buffer = new byte[1024];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            while (!inflater.finished()) {
                int count = inflater.inflate(buffer);
                outputStream.write(buffer, 0, count);
            }
        } catch (DataFormatException e) {
            throw exception(11000227, "处理svscp解压缩数据格式异常");
        } finally {
            inflater.end();
        }
        byte[] decompressedData = outputStream.toByteArray();
        return decompressedData;
    }


    /**
     * 解压文件
     */
    public static VersionInfo getVersionFromFile(byte[] data) {
        byte[] bytes = unzipWithoutHeader(data);
        VersionInfo version = new VersionInfo();
        try (DataInputStream dis = new DataInputStream(new ByteArrayInputStream(bytes))) {
            //版本标识
            byte[] versionLength = new byte[4];
            dis.readFully(versionLength);
            int versionInfoSize = ByteConvertUtil.byteToInt(versionLength);
            byte[] versionValue = new byte[versionInfoSize * 2];
            dis.readFully(versionValue);
            String versionVal = ByteConvertUtil.bytesToString(versionValue);
            version.setVersion(versionVal);
            //一级标识
            byte[] firstFlagLength = new byte[8];
            dis.readFully(firstFlagLength);
            long first = ByteBuffer.wrap(firstFlagLength).order(ByteOrder.LITTLE_ENDIAN).getLong();
            version.setFirst(first);
            //二级标识
            byte[] secondFlagLength = new byte[8];
            dis.readFully(secondFlagLength);
            long second = ByteBuffer.wrap(secondFlagLength).order(ByteOrder.LITTLE_ENDIAN).getLong();
            version.setSecond(second);
            //三级标识
            byte[] thirdFlagLength = new byte[8];
            dis.readFully(thirdFlagLength);
            long third = ByteBuffer.wrap(thirdFlagLength).order(ByteOrder.LITTLE_ENDIAN).getLong();
            version.setThird(third);
            return version;
        } catch (Exception e) {
            e.printStackTrace();
            throw exception(11000227, "Error: Unexpected end of file");
        }
    }

    /**
     * 解压文件
     *
     * @param outPath :解压路径
     */
    private static void unzipSqliteDBFile(byte[] data, String outPath) {
        try (DataInputStream dis = new DataInputStream(new ByteArrayInputStream(data))) {
            //版本标识
            byte[] versionLength = new byte[4];
            dis.readFully(versionLength);
            int versionInfoSize = ByteConvertUtil.byteToInt(versionLength);
            byte[] versionValue = new byte[versionInfoSize * 2];
            dis.readFully(versionValue);
            //一级标识
            byte[] firstFlagLength = new byte[8];
            dis.readFully(firstFlagLength);
            //二级标识
            byte[] secondFlagLength = new byte[8];
            dis.readFully(secondFlagLength);
            //三级标识
            byte[] thirdFlagLength = new byte[8];
            dis.readFully(thirdFlagLength);
            BufferedInputStream stream = new BufferedInputStream(dis);
            byte[] bytes = IoUtils.toByteArray(stream);

            FileUtil.writeBytes(bytes, outPath);
        } catch (Exception e) {
            e.printStackTrace();
            throw exception(11000227, "Error: Unexpected end of file");
        }
    }

    /**
     * 压缩文件
     *
     * @param dbUri  db文件地址
     * @param outUri 写出地址
     * @return 压缩后的字节数组
     */
    public static byte[] zipDBToFile(VersionInfo versionInfo, String dbUri, String outUri) {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             DataOutputStream dos = new DataOutputStream(bos)) {
            // 写入版本标识长度 (4字节)
            dos.write(ByteConvertUtil.intToByte(versionInfo.getVersion().length()));
            // 写入版本信息内容 (版本长度*2 字符串类型)
            dos.write(ByteConvertUtil.stringToByteAddWhitespace(versionInfo.getVersion()));
            // 写入一级标识 (8字节)
            dos.write(ByteConvertUtil.reverseByteArray(ByteConvertUtil.longToByteArray(versionInfo.getFirst())));
            // 写入二级标识 (8字节)
            dos.write(ByteConvertUtil.reverseByteArray(ByteConvertUtil.longToByteArray(versionInfo.getSecond())));
            // 写入三级标识 (8字节)
            dos.write(ByteConvertUtil.reverseByteArray(ByteConvertUtil.longToByteArray(versionInfo.getThird())));
            // 写入实际数据内容
            dos.write(FileUtil.readBytes(dbUri));
            dos.flush();
            // 将压缩后的内容写入指定输出文件
            return defLaterBytesToFile(bos.toByteArray(), outUri);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Error: Unable to compress file", e);
        }
    }

    /**
     * 压缩字节数据并写入文件
     *
     * @param data   要压缩的字节数组
     * @param outUri 输出文件路径
     */
    private static byte[] defLaterBytesToFile(byte[] data, String outUri) {
        Deflater deflater = new Deflater();
        deflater.setInput(data);
        deflater.finish();

        // 使用 ByteArrayOutputStream 来收集压缩后的数据
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(outputStream, deflater)) {

            // 将输入数据通过 DeflaterOutputStream 压缩
            deflaterOutputStream.write(data);
            deflaterOutputStream.finish();

            // 获取压缩后的数据
            byte[] compressedData = outputStream.toByteArray();
            if (StringUtils.isNotBlank(outUri)) {
                FileUtil.writeBytes(compressedData, outUri);
            }
            return compressedData;
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("压缩数据时发生错误: " + e.getMessage(), e);
        }finally {
            deflater.end();
        }
    }

}
