package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/20 10:03
 */
@Getter
public enum ClientProductEnum {
    PRODUCT_19("1-1-1", "19定额库"),
    PRODUCT_25("1-1-64", "25定额库"),
    ;
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    ClientProductEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }
}
