package cn.savas.hub.framework.common.util.collection;

import cn.savas.hub.framework.common.util.number.NumberUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

/**
 * 属性数据处理
 *
 * <AUTHOR>
 * @date 2020/6/27
 */
public class TreeDataUtil {


    @FunctionalInterface
    public interface GetFieldFunction<T, R> {
        /**
         * Get
         *
         * @param data
         * @return
         */
        R get(T data);
    }


    @FunctionalInterface
    public interface SetFieldFunction<T> {
        /**
         * Set
         *
         * @param data
         * @param children
         */
        void set(T data, List<T> children);
    }

    @FunctionalInterface
    public interface SetParentFunction<T> {
        /**
         * Set
         *
         * @param data
         */
        void set(T data, T parent);
    }


    @FunctionalInterface
    public interface SetCodeFunction<T> {
        /**
         * Set
         *
         * @param data
         */
        void set(T data, String code);
    }

    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTree(
            List<T> datas,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, String> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        return baseTree(datas, Boolean.FALSE, idFunction, pidFunction, setChildrenFunction, getChildrenFunction);
    }

    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T, ID> List<T> toTreeNoParentAdd(
            List<T> datas,
            GetFieldFunction<T, ID> idFunction,
            GetFieldFunction<T, ID> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        return baseTree(datas, Boolean.TRUE, idFunction, pidFunction, setChildrenFunction, getChildrenFunction);
    }

    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          设置父类
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTreeNoParentAdd(
            List<T> datas,
            SetParentFunction<T> setParentFunction,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, String> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        return baseTree(datas, Boolean.TRUE, idFunction, pidFunction, setChildrenFunction, getChildrenFunction, setParentFunction);
    }


    /**
     * 转换树形基础方法
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> baseTree(
            List<T> datas,
            Boolean isNoParentAdd,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, String> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction,
            SetParentFunction<T> setParentFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<String, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            String pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (StringUtils.isEmpty(pid) || "-1".equals(pid) || "0".equals(pid)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                if (isNoParentAdd) result.add(data);
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
            setParentFunction.set(data, pData);
        });

        return result;
    }


    /**
     * 转换树形基础方法
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T, ID> List<T> baseTree(
            List<T> datas,
            Boolean isNoParentAdd,
            GetFieldFunction<T, ID> idFunction,
            GetFieldFunction<T, ID> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<ID, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            ID pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (StringUtils.isEmpty(pid) || "-1".equals(pid) || "0".equals(pid)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                if (isNoParentAdd) result.add(data);
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }

    /**
     * 转换树形基础方法
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T, ID> List<T> baseTreeV2(
            List<T> datas,
            Boolean isNoParentAdd,
            GetFieldFunction<T, ID> idFunction,
            GetFieldFunction<T, ID> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<ID, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            ID pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (Objects.isNull(pid) || Objects.equals(pid, 0) || Objects.equals(pid, -1)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                if (isNoParentAdd) result.add(data);
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }

    /**
     * 转换树形基础方法
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T, ID> List<T> baseTreeV2Root(
            List<T> datas,
            ID root,
            GetFieldFunction<T, ID> idFunction,
            GetFieldFunction<T, ID> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<ID, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            ID pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (pid.equals(root)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }

    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTreeNumber(
            List<T> datas,
            GetFieldFunction<T, Number> idFunction,
            GetFieldFunction<T, Number> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<Number, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            Number pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (pid == null || pid.intValue() == 0) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }

    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTreeNumberNoParent(
            List<T> datas,
            GetFieldFunction<T, Number> idFunction,
            GetFieldFunction<T, Number> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<Number, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            Number pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (StringUtils.isEmpty(pid) || "-1".equals(pid) || "0".equals(pid)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                result.add(data);
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }

    /**
     * 转换树形可以传根节点值
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTreeManualRoot(
            List<T> datas,
            String root,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, String> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<String, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            String pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (!StringUtils.isEmpty(pid) && pid.trim().equals(root)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }


    /**
     * 转换树形
     *
     * @param datas               数据
     * @param idFunction          获取ID
     * @param pidFunction         获取父ID
     * @param setChildrenFunction 设置孩子数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> toTreeString(
            List<T> datas,
            String pKey,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, String> pidFunction,
            SetFieldFunction<T> setChildrenFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }

        List<T> result = new ArrayList<>();
        Map<String, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(idFunction.get(data), data);
        }

        datas.forEach(data -> {
            // 获取父ID
            String pid = pidFunction.get(data);
            // 父ID为空，表示为根节点
            if (StringUtils.isEmpty(pid) || pKey.equals(pid)) {
                result.add(data);
                return;
            }
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                return;
            }

            // 获取父数据
            T pData = dataMap.get(pid);

            List<T> children = getChildrenFunction.get(pData);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            setChildrenFunction.set(pData, children);
        });

        return result;
    }


    /**
     * 对集合和子项结合排序
     *
     * @param datas               数据
     * @param seqExtractor        获取排序字段
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> sort(
            List<T> datas,
            Function<T, Integer> seqExtractor,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        // 排序
        datas.sort(Comparator.comparing(seqExtractor, Comparator.comparing(NumberUtils::ifEmptyRetZero)));
        datas.forEach(data -> {
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(data))) {
                sort(getChildrenFunction.get(data), seqExtractor, getChildrenFunction);
            }
        });

        return datas;
    }


    public static <T> List<T> sortToList(List<T> sort, GetFieldFunction<T, List<T>> getChildrenFunction, SetFieldFunction<T> setChildrenFunction) {
        List<T> list = new ArrayList<>();
        getList(sort, list, getChildrenFunction);
        for (T t : list) {
            setChildrenFunction.set(t, null);
        }
        return list;
    }

    private static <T> void getList(List<T> sort, List<T> list, GetFieldFunction<T, List<T>> getChildrenFunction) {
        for (T t : sort) {
            list.add(t);
            if (getChildrenFunction.get(t) != null) {
                getList(getChildrenFunction.get(t), list, getChildrenFunction);
            }
        }

    }

    /**
     * 对集合和子项结合排序
     *
     * @param datas               数据
     * @param seqExtractor        获取排序字段
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> sortStringNo(
            List<T> datas,
            Function<T, String> seqExtractor,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        // 排序
        datas.sort(Comparator.comparing(seqExtractor, Comparator.comparing(NumberUtils::ifEmptyStringZero)));
        datas.forEach(data -> {
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(data))) {
                sortStringNo(getChildrenFunction.get(data), seqExtractor, getChildrenFunction);
            }
        });

        return datas;
    }

    /**
     * 对集合和子项结合排序
     *
     * @param datas               数据
     * @param seqExtractor        获取排序字段
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> sortDate(
            List<T> datas,
            Function<T, LocalDateTime> seqExtractor,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        // 排序
        datas.sort(Comparator.comparing(seqExtractor));
        datas.forEach(data -> {
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(data))) {
                sortDate(getChildrenFunction.get(data), seqExtractor, getChildrenFunction);
            }
        });

        return datas;
    }

    /**
     * 对集合和子项结合排序
     *
     * @param datas               数据
     * @param seqExtractor        获取排序字段
     * @param getChildrenFunction 获取孩子数据
     * @param <T>
     * @return
     */
    public static <T> List<T> sortUtilDate(
            List<T> datas,
            Function<T, Date> seqExtractor,
            GetFieldFunction<T, List<T>> getChildrenFunction) {

        // 排序
        datas.sort(Comparator.comparing(seqExtractor));
        datas.forEach(data -> {
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(data))) {
                sortUtilDate(getChildrenFunction.get(data), seqExtractor, getChildrenFunction);
            }
        });

        return datas;
    }

    /**
     * tree 转List
     *
     * @param datas               数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>                 turn
     */
    public static <T> List<T> treeToList(List<T> datas, GetFieldFunction<T, List<T>> getChildrenFunction) {
        List<T> list = new ArrayList<>();
        getTreeData(list, datas, getChildrenFunction);
        return list;
    }

    /**
     * tree 转List
     *
     * @param datas               数据
     * @param getChildrenFunction 获取孩子数据
     * @param <T>                 turn
     */
    public static <T> List<T> treeToList(List<T> datas, GetFieldFunction<T, List<T>> getChildrenFunction, SetFieldFunction<T> setChildrenFunction) {
        List<T> list = new ArrayList<>();
        getTreeData(list, datas, getChildrenFunction);
        list.forEach(p -> {
            setChildrenFunction.set(p, null);
        });
        return list;
    }

    private static <T> void getTreeData(List<T> list, List<T> datas, GetFieldFunction<T, List<T>> getChildrenFunction) {
        datas.forEach(data -> {
            list.add(data);
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(data))) {
                getTreeData(list, getChildrenFunction.get(data), getChildrenFunction);
            }
        });
    }

    /**
     * 获取所有子节点id集合
     *
     * @param tree
     * @param idFunction
     * @param getChildrenFunction
     * @param <T>
     * @return
     */

    public static <T> List<String> getAllChildren(
            List<T> tree,
            GetFieldFunction<T, String> idFunction,
            GetFieldFunction<T, List<T>> getChildrenFunction) {
        //递归获取所有子节点
        List<String> list = new ArrayList<>();
        for (T t : tree) {
            list.add(idFunction.get(t));
            if (!CollectionUtils.isEmpty(getChildrenFunction.get(t))) {
                list.addAll(getAllChildren(getChildrenFunction.get(t), idFunction, getChildrenFunction));
            }
        }
        return list;
    }

    /**
     * 递归更新tree 数据值
     * 已知所有子集节点数据
     *
     * @param node
     * @param childrenGetter
     * @param <T>
     * @return
     */
    public static <T> BigDecimal getTreeDataValue(T node, Function<T, List<T>> childrenGetter, GetFieldFunction<T, BigDecimal> getValueFunction) {
        List<T> children = childrenGetter.apply(node);
        if (children == null || children.isEmpty()) {
            return getValueFunction.get(node) == null ? BigDecimal.ZERO : getValueFunction.get(node);
        }
        BigDecimal total = new BigDecimal(0);
        for (T child : children) {
            total = total.add(getTreeDataValue(child, childrenGetter, getValueFunction));
        }
        return total;
    }

    /**
     * 递归更新tree 序号 重新排列tree 序号
     * example :1,1.1,1.2,1.3
     * 已知所有子集节点数据
     */
    public static <T> void rebuildDataCode(List<T> tree, SetCodeFunction<T> codeFunction, Function<T, List<T>> childrenGetter, String pCode) {
        if (StringUtils.isEmpty(pCode)) {
            pCode = "";
        }
        for (int i = 0; i < tree.size(); i++) {
            T node = tree.get(i);
            String childCode = !pCode.isEmpty() ? pCode + "." + (i + 1) : String.valueOf(i + 1);
            codeFunction.set(node, childCode);
            List<T> children = childrenGetter.apply(node);
            if (children != null && !children.isEmpty()) {
                rebuildDataCode(children, codeFunction, childrenGetter, childCode);
            }
        }
    }
}

