package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * 客户端数据字段名称枚举
 *
 * <AUTHOR>
 */
@Getter
public enum EnumUnZipFieldName {

    /**
     * 单重
     */
    DJ_DZ("DJ_DZ", "单重"),

    /**
     * 总重
     */
    DZ("DZ", "总重"),

    /**
     * 单价-安装费
     */
    DJ_AZF("DJ_AZF", "单价-安装费"),

    /**
     * 单价-设备费
     */
    DJ_SBF("DJ_SBF", "单价-设备费"),

    /**
     * 单价-主材单价
     */
    DJ_ZCF("DJ_ZCF", "单价-主材单价"),

    /**
     * 单价-直接费单价
     */
    DJ_ZJF("DJ_ZJF", "单价-直接费单价"),

    /**
     * 单价-人工单价
     */
    DJ_RGF("DJ_RGF", "单价-人工单价"),

    /**
     * 单价-材料单价
     */
    DJ_CLF("DJ_CLF", "单价-材料单价"),

    /**
     * 单价-机械单价
     */
    DJ_JX<PERSON>("DJ_JXF", "单价-机械单价"),

    /**
     * 单价-主要材料费单价
     */
    DJ_ZCGZF("DJ_ZCGZF", "单价-主要材料费单价"),


    /**
     * 单价-设备购置费单价
     */
    DJ_SBGZF("DJ_SBGZF", "单价-设备购置费单价"),

    /**
     * 单价-非标设计费单价
     */
    DJ_FBSJF("DJ_FBSJF", "单价-非标设计费单价"),

    /**
     * 单价-综合费单价
     */
    DJ_ZHQF("DJ_ZHQF", "单价-综合费单价"),

    /**
     * 单价-主材含税价
     */
    DJ_ZCHSJ("DJ_ZCHSJ", "单价-主材含税价"),

    /**
     * 单价-设备含税价
     */
    DJ_SBHSJ("DJ_SBHSJ", "单价-设备含税价"),

    /**
     * 合价-设备费
     */
    SBF("SBF", "合价-设备费"),

    /**
     * 合价-主材单价费
     */
    ZCF("ZCF", "合价-主材单价费"),

    /**
     * 合价-直接费合价
     */
    ZJF("ZJF", "合价-直接费合价"),

    /**
     * 合价-人工合价 (11.09日根据解压出的价格信息,改Key为RGF)
     */
    RGF("RGF", "合价-直接费合价"),

    /**
     * 合价-材料合价
     */
    CLF("CLF", "合价-材料合价"),

    /**
     * 合价-机械合价
     */
    JXF("JXF", "合价-机械合价"),

    /**
     * 费用合计-设备购置费
     */
    SBGZF("SBGZF", "费用合计-设备购置费"),

    /**
     * 进口-设备购置费
     */
    JKSBGZF("JKSBGZF", "进口-设备购置费"),

    /**
     * 费用合计-主要材料费
     */
    ZCGZF("ZCGZF", "费用合计-主要材料费"),

    /**
     * 进口-主要材料费
     */
    JKZCGZF("JKZCGZF", "进口-主要材料费"),

    /**
     * 费用合计-安装费
     */
    AZF("AZF", "费用合计-安装费"),

    /**
     * 费用合计-综合费
     */
    ZHQF("ZHQF", "费用合计-综合费"),

    /**
     * 费用合计-建筑工程费
     */
    JZGCF("JZGCF", "费用合计-建筑工程费"),

    /**
     * 费用合计-辅材费
     */
    FCF("FCF", "费用合计-辅材费"),

    /**
     * 工程费设备购置费（项目概算设备费）
     */
    COST_SBF("COST_SBF", "工程费设备购置费"),

    /**
     * 工程费主材费
     */
    COST_ZCF("COST_ZCF", "工程费主材费"),

    /**
     * 工程费安装费
     */
    COST_AZF("COST_AZF", "工程费安装费"),

    /**
     * 工程费建筑费
     */
    COST_JZF("COST_JZF", "工程费建筑费"),

    /**
     * 工程费外币
     */
    COST_WBF("COST_WBF", "工程费外币"),

    /**
     * 其他费金额
     */
    COST_QTF("COST_QTF", "其他费金额"),

    /**
     * 其他外币费（含外币金额）
     */
    QTWBF("QTWBF", "其他外币费"),

    /**
     * 工程费合计
     */
    GCF("GCF", "工程费"),

    /**
     * 工程费合计
     */
    SHGCF("GCF", "工程费用"),
    /**
     * 主要材料费
     */
    ZYCLF("ZCGZF", "主要材料费"),

    /**
     * 外币单价-设备费
     */
    DJ_WBSBF("DJ_WBSBF", "外币单价-设备费"),

    /**
     * 外币单价-主材费
     */
    DJ_WBZCF("DJ_WBZCF", "外币单价-主材费"),

    /**
     * 外币单价-海运费
     */
    DJ_WBHYF("DJ_WBHYF", "外币单价-海运费"),

    /**
     * 外币单价-海运保险费
     */
    DJ_WBYBF("DJ_WBYBF", "外币单价-海运保险费"),

    /**
     * 外币金额-设备费
     */
    WBSBF("WBSBF", "外币金额-设备费"),

    /**
     * 外币金额-主材费
     */
    WBZCF("WBZCF", "外币金额-主材费"),

    /**
     * 外币金额-海运费
     */
    WBHYF("WBHYF", "外币金额-海运费"),

    /**
     * 外币金额-海运保险费
     */
    WBYBF("WBYBF", "外币金额-海运保险费"),

    /**
     * 外币金额-外币合价
     */
    WBF("WBF", "外币金额-外币合价"),

    /**
     * 折合人民币-设备费
     */
    JKSBF("JKSBF", "折合人民币-设备费"),

    /**
     * 折合人民币-主材费
     */
    JKZCF("JKZCF", "折合人民币-主材费"),

    /**
     * 折合人民币-海运费
     */
    HYF("HYF", "折合人民币-海运费"),

    /**
     * 折合人民币-海运保险费
     */
    YBF("YBF", "折合人民币-海运保险费"),

    /**
     * 折合人民币-人民币合计
     */
    RMBF("RMBF", "折合人民币-人民币合计"),

    /**
     * 从属费-关税
     */
    GS("GS", "从属费-关税"),

    /**
     * 从属费-外贸手续费
     */
    WMSXF("WMSXF", "从属费-外贸手续费"),

    /**
     * 从属费-银行财务费
     */
    YHCWF("YHCWF", "从属费-银行财务费"),

    /**
     * 人民币-国内运费
     */
    YSF("YSF", "人民币-国内运费"),

    /**
     * 人民币-国内保管费
     */
    BGF("BGF", "人民币-国内保管费"),

    /**
     * 进口-小计
     */
    JK_XJ("JK_XJ", "进口-小计"),

    /**
     * 人民币-合计
     */
    GZF("GZF", "人民币-合计"),

    /**
     * 其他费增值税
     */
    QTFZZS("QTFZZS", "其他费增值税"),

    /**
     * 工程费-增值税
     */
    qf_ZZS("qf_ZZS", "工程费-增值税"),

    /**
     * 工程费-增值税
     */
    ZZSHJ("ZZSHJ", "工程费-增值税"),

    /**
     * 取费-增值税
     */
    qf_ZZSHJ("qf_ZZSHJ", "取费-增值税"),

    /**
     * 主材增值税
     */
    ZCZZS("ZCZZS", "主材增值税"),

    /**
     * 进口主材增值税
     */
    JKZCZZS("JKZCZZS", "进口主材增值税"),

    /**
     * 设备增值税
     */
    SBZZS("SBZZS", "设备增值税"),

    /**
     * 进口设备增值税
     */
    JKSBZZS("JKSBZZS", "进口设备增值税"),

    /**
     *
     */
    JZ_SGZZS("JZ_SGZZS", ""),

    /**
     *
     */
    qf_JZ_SGZZS("qf_JZ_SGZZS", ""),

    /**
     *
     */
    AZ_SGZZS("AZ_SGZZS", ""),

    /**
     *
     */
    qf_AZ_SGZZS("qf_AZ_SGZZS", ""),

    /**
     *安全生产费
     */
    qf_AQSCF("qf_AQSCF", "安全生产费"),

    /**
     * 仓库运营费
     */
    qf_warehouse("qf_warehouse","仓库运营费"),

    /**
     *工程费
     */
    qf_GCF("qf_GCF", "工程费"),

    /**
     *工程费:固定资产其他费
     */
    qf_GDZCQTF("qf_GDZCQTF", "固定资产其他费"),

    /**
     *  安装-安全生产费
     */
    qf_AZAQSCF("qf_AZAQSCF", "安装-安全生产费"),

    /**
     *  安装-安全生产费
     */
    qf_JZAQSCF("qf_JZAQSCF", "建筑-安全生产费"),
    /**
     * 其他-增值税
     */
    ZZS("ZZS", "人民币-增值税"),

    /**
     * 主材-采保费
     */
    ZCCBF("ZCCBF", "主材采保费"),

    /**
     * 设备-采保费
     */
    SBCBF("SBCBF", "设备采保费"),

    /**
     * 主材-运输费
     */
    ZCYSF("ZCYSF", "主材运输费"),

    /**
     * 设备-运输费
     */
    SBYSF("SBYSF", "设备运输费");

    /**
     * 编码
     */
    private String code;
    /**
     * 值
     */
    private String value;

    EnumUnZipFieldName(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static EnumUnZipFieldName getEnumByCode(String code) {
        for (EnumUnZipFieldName value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
