package cn.savas.hub.framework.common.client.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.util.List;

@Slf4j
public class ByteConvertUtil {

    /**
     * 将 int 转换为字节数组
     */
    public static byte[] intToByteArray(int value) {
        ByteBuffer buffer = ByteBuffer.allocate(4);
        buffer.putInt(value);
        return buffer.array();
    }

    /**
     * 反转字节数组
     */
    public static byte[] reverseByteArray(byte[] byteArray) {
        byte[] reversed = new byte[byteArray.length];
        for (int i = 0; i < byteArray.length; i++) {
            reversed[i] = byteArray[byteArray.length - 1 - i];
        }
        return reversed;
    }

    /**
     * 将字节数组转换回 int
     */
    public static int byteArrayToInt(byte[] byteArray) {
        ByteBuffer buffer = ByteBuffer.wrap(byteArray);
        return buffer.getInt();
    }


    /**
     * 字节数组到int的转换.
     */
    public static int byteToInt(byte[] b) {
        int s = 0;
        // 最低位
        int s0 = b[0] & 0xff;
        int s1 = b[1] & 0xff;
        int s2 = b[2] & 0xff;
        int s3 = b[3] & 0xff;
        s3 <<= 24;
        s2 <<= 16;
        s1 <<= 8;
        s = s0 | s1 | s2 | s3;
        return s;
    }

    /**
     * 字节数组到String的转换.
     */
    public static String bytesToString(byte[] str) {
        String keyword = null;
        try {
            keyword = new String(str, "GBK").replaceAll("\\u0000", "");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return keyword;
    }

    //byte字节 转16位字符串并补0
    public static String bytesToString(byte b) {
        return String.format("%02X", b & 0xFF);
    }


    //由于取值范围不一致需要转码//转为无符号整数
    public static byte[] convertToClient(byte[] all) {
        for (int i = 0; i < all.length; i++) {
            all[i] = (byte) (all[i] & 0xFF);
        }
        return all;
    }

    /**
     * 将 long 转换为字节数组
     *
     * @param value long 值
     * @return 字节数组
     */
    public static byte[] longToByteArray(long value) {
        byte[] byteArray = new byte[8];
        for (int i = 0; i < 8; i++) {
            byteArray[i] = (byte) (value >>> (56 - (i * 8)));
        }
        return byteArray;
    }

    /**
     * int到字节数组的转换.
     */
    public static byte[] intToByte(int number) {
        int temp = number;
        byte[] b = new byte[4];
        for (int i = 0; i < b.length; i++) {
            // 将最低位保存在最低位
            b[i] = new Integer(temp & 0xff).byteValue();
            temp = temp >> 8;// 向右移8位
        }
        return b;
    }

    /**
     * string到字节数组的转换.
     */
    public static byte[] stringToByteAddWhitespace(String str) {
        byte[] gbks;
        try {
            gbks = str.getBytes("GBK");
            // 处理字节数组 gbks
        } catch (UnsupportedEncodingException e) {
            // 异常处理逻辑，例如打印错误信息或使用默认编码
            log.error("GBK 编码不支持: " + e.getMessage());
            // 可选：使用默认编码（如 UTF-8）作为备选
            gbks = str.getBytes(); // 默认使用平台编码
        }
        byte[] toLocal = new byte[gbks.length * 2];
        for (int i = 0; i < gbks.length; i++) {
            toLocal[i * 2] = gbks[i];
            toLocal[i * 2 + 1] = 0;
        }
        return toLocal;
    }

    /**
     * 合并数组
     *
     * @return
     */
    public static byte[] combineArrays(List<byte[]> bytes) {
        int massLength = 0;
        for (byte[] b : bytes) {
            massLength += b.length;
        }
        byte[] c = new byte[massLength];
        byte[] d;
        int index = 0;
        for (byte[] anA : bytes) {
            d = anA;
            System.arraycopy(d, 0, c, 0 + index, d.length);
            index += d.length;
        }
        return c;
    }

    /**
     * 倒序获取
     *
     * @param bytes
     * @param index
     * @return
     */
    public static String getBinaryString(byte[] bytes, int index) {
        StringBuffer buffer = new StringBuffer();
        for (int i = index; i >= 0; i--) {
            buffer.append(String.format("%02X", bytes[i] & 0xFF));
        }
        return buffer.toString();
    }

    /**
     * 字节数组到 long 的转换
     */
    public static long byteToLong(byte[] b) {
        long result = 0;
        for (int i = 0; i < 8; i++) {
            result |= ((long)(b[i] & 0xff)) << (56 - (i * 8));
        }
        return result;
    }
}
