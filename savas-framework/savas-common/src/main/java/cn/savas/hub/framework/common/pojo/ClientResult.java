package cn.savas.hub.framework.common.pojo;

import cn.savas.hub.framework.common.exception.enums.GlobalErrorCodeConstants;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/22 16:18
 */
@Data
public class ClientResult<T> implements Serializable {

    private static final long serialVersionUID = -7416238114526357365L;

    private T jsondata;
    private Integer level;// 0:success
    private String msg;

    public static <T> ClientResult<T> success(T data){
        ClientResult<T> result = new ClientResult<>();
        result.jsondata = data;
        result.level = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.msg = "";
        return result;
    }

    //error
    public static <T> ClientResult<T> error(Integer level, String msg, T data) {
        ClientResult<T> result = new ClientResult<>();
        result.level = level;
        result.msg = msg;
        result.jsondata = data;
        return result;
    }
}
