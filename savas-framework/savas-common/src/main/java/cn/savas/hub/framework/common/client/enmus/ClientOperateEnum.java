package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/27 11:02
 */
@Getter
public enum ClientOperateEnum {
    NO_CHANGE(0, "没变化"),
    INSERT(1, "插入"),
    UPDATE(2, "修改"),
    DELETE(3, "删除");

    private final Integer code;
    private final String desc;

    ClientOperateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClientOperateEnum getByCode(Integer code) {
        for (ClientOperateEnum value : ClientOperateEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
