package cn.savas.hub.framework.common.client.util;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class Test22 {

    public static void main(String[] args) throws UnsupportedEncodingException {
        Map<String, BigDecimal> map = new HashMap<>();
        map.put("AQSCF0", BigDecimal.valueOf(0));
        map.put("AQSCF7", BigDecimal.valueOf(1.0));
        map.put("AQSCF01", BigDecimal.valueOf(-0));
        map.put("AQSCF1", BigDecimal.valueOf(-0.118));
        map.put("AQSCF2", BigDecimal.valueOf(-1.111));
        map.put("AQSCF3", BigDecimal.valueOf(-12.111));
        map.put("AQSCF4", BigDecimal.valueOf(1.222));
        map.put("AQSCF5", BigDecimal.valueOf(12.22));
        map.put("AQSCF6", BigDecimal.valueOf(0.333));
        byte[] bytes = V9MapToBinaryUtil.convertToBinary(map);
       Map<String, BigDecimal> map1 = V9BinaryToMapUtil.unpackData(bytes);
        for (String key : map1.keySet()) {
            System.out.println(key + ":" + map1.get(key));
        }

    }

}
