package cn.savas.hub.framework.common.mapstruct;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2024/12/6 14:38
 */
public class DateTimeConverter {
    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter YYYY_MM_DD_T_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
    public static LocalDateTime toLocalDateTime(String dateTimeStr) {
        if (dateTimeStr != null) {
            // 标准格式解析
            if (dateTimeStr.endsWith("Z")) {
                return ZonedDateTime.parse(dateTimeStr).toLocalDateTime();
            } else {
                return LocalDateTime.parse(dateTimeStr, YYYY_MM_DD_T_HH_MM_SS);
            }
        }
        return null;
    }

    public static String of(LocalDateTime dateTime) {
        if (dateTime != null) {
            return dateTime.format(YYYY_MM_DD_HH_MM_SS);
        }
        return null;
    }
}
