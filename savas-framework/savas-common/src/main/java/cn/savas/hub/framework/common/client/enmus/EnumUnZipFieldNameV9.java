package cn.savas.hub.framework.common.client.enmus;

import cn.hutool.core.util.NumberUtil;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 客户端数据字段名称枚举
 *
 * <AUTHOR>
 */
@Getter
public enum EnumUnZipFieldNameV9 {
    MYSBF("MY_SBF", "设备单价-进口（美元）"),
    MYZCF("MY_ZCF", "主材单价-进口（美元）"),
    ZYCLF("ZYCLF", "主要材料费"),
    JK_ZYCLF("JKZYCLF", "主要材料费-进口"),
    SBGZF("SBGZF", "设备购置费"),
    JK_SBGZF("JKSBGZF", "设备购置费-进口"),
    SGZZS("SGZZS", "设备购置费增值税"),
    ZZSF("ZZSF", "增值税费"),
    QF_ZZSHJ("qf_ZZSHJ", "取费-增值税"),
    JKZZSF("JKZZSF", "进口增值税费"),
    JKSBYSF("JKSBYSF", "设备运输费-进口"),
    JKZCYSF("JKZCYSF", "主材运输费-进口"),
    JKSBBGF("JKSBBGF", "设备保管费-进口"),
    JKZCBGF("JKZCBGF", "主材保管费-进口"),
    JKSBGZF("JKSBGZF", "设备购置费-进口"),
    JKZYCLF("JKZYCLF", "主要材料费-进口"),
    CZSBF("CZSBF", "设备费-成本（元）"),
    CZZCF("CZZCF", "主材费-成本（元）"),
    JKSBF("JKSBF", "设备费-进口"),
    JKZCF("JKZCF", "主材费-进口"),
    RMB_HYF("RMB_HYF", "海运费-人民币"),
    AZ_ZHF("AZ_ZHF", "综合费-安装（元）"),
    JZ_ZHF("JZ_ZHF", "综合费-建筑（元）"),
    FEE("FEE", "工程费"),
    WBF("WBF", "外币费"),
    ZHF("ZHF", "综合费"),
    QTFY("QTFY", "其他费用"),
    AZFCZ("AZFCZ", "如果勾选吨单价并且原单位是吨这个参数就有值"),
    ZCFCZ("ZCFCZ", "如果勾选吨单价这个参数就有值"),
    DJ_MY_SBF("DJ_MY_SBF", "设备单价-进口（美元）"),
    DJ_MY_ZCF("DJ_MY_ZCF", "主材单价-进口（美元）"),
    WBSBF("WBSBF", "设备费-外币"),
    WBZCF("WBZCF", "主材费-外币"),
    GSSL("GSSL", "关税税率"),
    DZ("DZ", "总重"),
    DJ_RGF("DJ_RGF","单价-人工单价"),
    DJ_CLF("DJ_CLF","单价-材料单价"),
    DJ_JXF("DJ_JXF","单价-机械单价"),
    DJ_ZCF("DJ_ZCF","单价-主材单价"),
    DJ_SBF("DJ_SBF","单价-设备费"),
    DJ_ZJF("DJ_ZJF","单价-直接费单价"),
    SBF("SBF","合价-设备费"),
    ZCF("ZCF","合价-主材单价费"),
    ZJF("ZJF","合价-直接费合价"),
    RGF("RGF","合价-直接费合价"),
    CLF("CLF","合价-材料合价"),
    JXF("JXF","合价-机械合价"),
    ZHQF("ZHQF","费用合计-综合费"),
    GS("GS","从属费-关税"),
    WMSXF("WMSXF","从属费-外贸手续费"),
    YHCWF("YHCWF", "从属费-银行财务费"),
    GZF("GZF","人民币-合计"),
;

    /**
     * 编码
     */
    private String code;/**
     * 值
     */
    private String value;

    EnumUnZipFieldNameV9(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static EnumUnZipFieldNameV9 getEnumByCode(String code) {
        for (EnumUnZipFieldNameV9 value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 多值相加
     */
    public static BigDecimal sum(Map<String, BigDecimal> params, EnumUnZipFieldNameV9... val1) {
        BigDecimal sum = BigDecimal.ZERO;
        for (EnumUnZipFieldNameV9 val : val1) {
            sum = NumberUtil.add(sum, params.get(val.getCode()));
        }
        return sum;
    }
}
