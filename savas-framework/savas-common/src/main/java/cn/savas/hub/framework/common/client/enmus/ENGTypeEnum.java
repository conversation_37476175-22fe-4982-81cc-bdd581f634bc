package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * PRJ_Engineering表eng_Scheme类型枚举
 *
 * <AUTHOR>
 * 禁止使用，此类由ClientClassIdEnum枚举代替。
 *
 */
@Getter
public enum ENGTypeEnum {

    /**
     * 项目
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    PROJECT(0, "项目"),

    /**
     * 分包
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    FENBAO(84037632, "分包"),

    /**
     * 单项
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    DANXIANG(84082688, "单项"),

    /**
     * 分项
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    FENXIANG(84086784, "分项"),

    /**
     * 单位
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    DANWEI(84148224, "单位"),

    /**
     * 分部
     * 禁止使用，此枚举后续由ClientClassIdEnum枚举代替。
     */
    @Deprecated
    FENBU(84213760, "分部"),
    ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 值
     */
    private String value;

    ENGTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static ENGTypeEnum getEnumByCode(Integer code) {
        for (ENGTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Integer getEnumCodeByType(Integer type) {
        switch (type) {
            case 2:
                return 84082688;
            case 4:
                return 84148224;
            default:
                return 84037632;
        }
    }

}
