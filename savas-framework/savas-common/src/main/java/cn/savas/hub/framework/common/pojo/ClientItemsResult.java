package cn.savas.hub.framework.common.pojo;

import cn.savas.hub.framework.common.exception.enums.GlobalErrorCodeConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 16:18
 */
@Data
public class ClientItemsResult<T> implements Serializable {

    private static final long serialVersionUID = 7662435029971774325L;

    private Map<String, T> jsondata;
    private Integer level;// 0:success
    private String msg;

    public static <T> ClientItemsResult<T> success(T data){
        ClientItemsResult<T> result = new ClientItemsResult<>();
        Map<String, T> items = new HashMap<>();
        items.put("items", data);
        result.jsondata = items;
        result.level = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.msg = "";
        return result;
    }
    //error
    public static <T> ClientItemsResult<T> error(Integer level, String msg) {
        ClientItemsResult<T> result = new ClientItemsResult<>();
        result.level = level;
        result.msg = msg;
        result.jsondata = new HashMap<>();
        return result;
    }
}
