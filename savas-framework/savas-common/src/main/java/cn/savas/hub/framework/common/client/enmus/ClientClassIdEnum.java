package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/6 10:44
 */
@Getter
public enum ClientClassIdEnum {
    SERVICE_MIRROR("工程项目的服务端镜像", 83886080L), // $05000000
    PROJECTENG("工程项目", 83951616L), // $05010000
    PROJECTCOST("其他费用", 84017152L), // $05020000
    ENGINEERINGCOST("工程费用", 84021248L), // $05021000
    GROUPENG("群组工程", 84037632L), // $05025000
    SECTIONENG("单项工程", 84082688L), // $05030000
    SUBSECTIONENG("分项工程", 84086784L), // $05031000
    UNITENG("单位工程", 84148224L), // $05040000
    SPECIALTY("单位工程的专业分支", 84213760L), // $05050000
    SECTIONSUBITEM("分部", 84344832L), // $05070000
    BILL("清单", 84410368L), // $05080000
    MEASURE("措施", 84475904L), // $05090000
    OTHERBILLCATEGORY("其他项目清单分组", 84541440L), // $050A0000
    OTHERBILL("其他项目清单", 84545536L), // $050A1000
    INDEPENDENTCATEGORY("独立费清单分组", 84549632L), // $050A2000
    INDEPENDENTCOST("独立费清单", 84553728L), // $050A3000
    NORM("定额子目", 84606976L), // $050B0000
    NORMCONVERT("定额子目标准换算", 84672512L), // $050C0000
    CONSUMPTION("工料机", 84738048L), // $050D0000
    CONSUMPTIONCONVERT("非标设备换算", 84742144L), // $050D1000
    VARIATEITEM("变量条目", 84746240L), // $050D2000
    AMOUNTDETAILITEM("工程量明细条目", 84750336L), // $050D3000
    PROJECTPARAMETER("项目属性", 84803584L), // $050E0000
    EXPENSEITEM("单价公式科目", 84869120L), // $050F0000
    EXPENSE("", 84934656L), // $05100000
    DESIGNCONDITION("设计条件", 85065728L), // $05120000
    DESIGNCONDITIONOPTION("设计条件Option的属性项", 85069824L), // $05121000
    DESIGNCONDITIONXLSROW("设计条件XLSRow数据", 85073920L), // $05122000
    CONSUMPTIONSUMMARY("工料机汇总", 99614720L), // $05F00000
    OTHERFEEITEMCALCULATOR("其他费计算器", 85983232L), // $05200000
    ENGINEERINGLECTURE("编制说明", 87031808L), // $05300000

    RATESTANDARDOPTIONCODE("费率设置左侧设置项", 84758528L), // $050D5000
    RATESTANDARDOPTIONCODECONTENT("费率设置左侧设置项明细", 84762624L), // $050D6000
    REVIEWMINUTE("审查纪要条目", 89194496L), // $05510000
    REVIEWTRACE("审查调整记录", 89260032L),// $05520000

    MODELPARAMETER("参数", 33558528L),
    CODEITEM("标准代码", 33562624L),
    USERTEMPLATE("用户数据模板", 33566720L),
    FILEATTACHMENT("文件附件", 33570816L),
    SUGGESTION("意见", 33574912L),
    UNIT("计量单位", 33579008L),
    USERPROJECTFILE("用户项目文件", 33583104L),
    PROJECT("项目", 33587200L),
    HISTORYDATA("业务对象的历史数据", 33591296L),
    MODELCOMMENT("批注", 33595392L),
    ROLE("角色", 16781312L),
    USER("用户", 16785408L),
    USERROLE("用户角色", 16789504L),
    ACTION("系统功能", 16793600L),
    FUNCTIONAUTHORITY("功能权限", 16797696L),
    DATAAUTHORITY("数据权限", 16801792L),
    ORGANIZATION("工作单位", 16805888L),
    LINKMAN("联系人", 16809984L),
    COMPANY("联系单位，客户，供应商，配送商，生产商，维修商等", 16814080L),
    LOGINUSER("登录用户", 16818176L),
    LOG("日志", 16822272L),
    NOTICE("通知", 16826368L),
    FLOWNODE("工作流节点", 16830464L),
    FLOWSCHEDULE("流程进度", 16834560L),
    FLOWNODEDIRECTION("流程执行方向", 16838656L),
    WORKFLOW("工作流", 16842752L),
    TEMPLATEFLOWNODE("流程模板", 16846848L),
    TEMPLATEFLOWNODEDIRECTION("流程模板的执行方向", 16850944L),
    RATESTANDARD("项目费率设置", 88080384L),
    SYSTEMFUNCTION("系统功能", 16793600L),
    ;

    private final String name;
    private final Long code;

    ClientClassIdEnum(String name, Long code) {
        this.name = name;
        this.code = code;
    }

    public static ClientClassIdEnum getByCode(Long code) {
        for (ClientClassIdEnum clientClassIdEnum : ClientClassIdEnum.values()) {
            if (clientClassIdEnum.getCode().equals(code)) {
                return clientClassIdEnum;
            }
        }
        return null;
    }

    public static ClientClassIdEnum getEnum(Long code) {
        for (ClientClassIdEnum clientClassIdEnum : ClientClassIdEnum.values()) {
            if (clientClassIdEnum.getCode() == code) {
                return clientClassIdEnum;
            }
        }
        return null;
    }

    public static ClientClassIdEnum getEnumByCode(Long wbsType) {
        for (ClientClassIdEnum value : values()) {
            if (value.code.equals(wbsType)) {
                return value;
            }
        }
        return null;
    }
}
