package cn.savas.hub.framework.common.client.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:10
 */
@Getter
public enum ClientParameterEnum {
    /**
     * 设计阶段
     */
    DESIGNSTAGE("设计阶段", "P_DESIGNSTAGE"),
    /**
     * 主材系数
     */
    MAINMATERIALCOEFFICIENT("主材系数", "MainMaterialCoefficient"),
    ;

    private final String name;
    private final String code;

    ClientParameterEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static ClientParameterEnum getByCode(String paraCode) {
        for (ClientParameterEnum value : ClientParameterEnum.values()) {
            if (value.getCode().equals(paraCode)) {
                return value;
            }
        }
        return null;
    }
}
