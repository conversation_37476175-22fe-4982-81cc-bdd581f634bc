package cn.savas.hub.framework.common.util.io;

import com.alibaba.fastjson.JSON;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class XStreamUtil {

    /**
     * 读文件转换成bean
     * @throws IOException
     */
    public static <T> T XML2Bean(File file, Class<T> clazz) throws IOException {

        FileInputStream inputStream = new FileInputStream(file);

        return XML2Bean(inputStream, clazz);
    }

    /**
     * 字符串转换成bean
     * @param <T>
     */
    public static <T> T XML2Bean(String xml, Class<T> clazz)   {

        if (StringUtils.isBlank(xml)) {
            return null;
        }

        XStream stream = new XStream(new DomDriver());

        XStream.setupDefaultSecurity(stream);

        stream.ignoreUnknownElements();

        stream.allowTypesByRegExp(new String[]{".*"});

        stream.processAnnotations(clazz);

        T t = (T) stream.fromXML(xml);
        //避免空属性
        return JSON.parseObject(JSON.toJSONString(t),clazz);
    }

    /**
     * 读文件流转换成bean
     * @param <T>
     */
    public static <T> T XML2Bean(FileInputStream inputStream, Class<T> clazz) {

        XStream stream = new XStream(new DomDriver());

        XStream.setupDefaultSecurity(stream);

        stream.ignoreUnknownElements();

        stream.allowTypesByRegExp(new String[]{".*"});

        stream.processAnnotations(clazz);

        T t = (T) stream.fromXML(inputStream);
        //避免空属性
        return JSON.parseObject(JSON.toJSONString(t),clazz);
    }
}
