package cn.savas.hub.framework.common.client.util;


import cn.hutool.core.util.ZipUtil;
import cn.savas.hub.framework.common.exception.ServerException;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;


/**
 * <AUTHOR>
 * @date
 */
public class V9BinaryToMapUtil {

    public static BigDecimal unZipDataToDto(String code, Map<String, BigDecimal> unzipMap) {
        return unzipMap.getOrDefault(code, BigDecimal.ZERO);
    }

    public static Map<String, BigDecimal> unpackData(byte[] cipherData) {
        return unpackData(cipherData, (BigDecimal value) -> value);
    }

    public static Map<String, BigDecimal> unpackData(byte[] cipherData, Function<BigDecimal, BigDecimal> valueFunction) {
        Map<String, BigDecimal> mmp = new HashMap<>(0);
        if (!Objects.isNull(cipherData) && cipherData.length > 0) {
            cipherData = ZipUtil.unZlib(cipherData);
            try (DataInputStream dis = new DataInputStream(new ByteArrayInputStream(cipherData))) {
                //KV数量
                byte[] size = new byte[4];
                dis.readFully(size);
                int kvSize = ByteConvertUtil.byteToInt(size);
                for (int i = 0; i < kvSize; i++) {
                    //key长度
                    byte[] keyLength = new byte[4];
                    dis.readFully(keyLength);
                    int keySize = ByteConvertUtil.byteToInt(keyLength);
                    //key
                    byte[] key = new byte[keySize * 2];
                    dis.readFully(key);
                    //类型(直接跳过)
                    byte[] type = new byte[4];
                    dis.readFully(type);
                    //value
                    byte[] valueByte = new byte[10];
                    dis.readFully(valueByte);
                    BigDecimal value = convertToBigDecimal(valueByte);
                    mmp.put(ByteConvertUtil.bytesToString(key), valueFunction.apply(value));
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServerException(11000227, "Error: Unexpected end of file");
            }
        }
        return mmp;
    }

    /**
     * byte BigDecimal
     *
     * @param bytes
     * @return
     */
    public static BigDecimal convertToBigDecimal(byte[] bytes) {
        //获取符号位
        Boolean sign = (bytes[9] & 0xFF) > 128;
        //获取尾数位
        String code = ByteConvertUtil.getBinaryString(bytes, 7);
        //将十六进制字符串转为 BigInteger
        BigInteger bigIntegerValue = new BigInteger(code, 16);
        //0的情况
        if (bytes[8] == 0) {
            return BigDecimal.ZERO;
        }
        //区分 1.48 1.点几
        if (bytes[8] == -1) {
            BigDecimal result = new BigDecimal(bigIntegerValue).divide(BigDecimal.valueOf(Math.pow(2, 63)), MathContext.DECIMAL128).add(new BigDecimal("0.0000001")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
            return sign ? result.negate() : result;
        }
        //区分 0.48 0.点几
        if (bytes[8] < -1) {
            BigDecimal result = new BigDecimal(bigIntegerValue).divide(BigDecimal.valueOf(Math.pow(2, 62 - bytes[8])), MathContext.DECIMAL128).add(new BigDecimal("0.0000001")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
            return sign ? result.negate() : result;
        }
        //获取指数位 正常数
        String exponent = sign ? ByteConvertUtil.bytesToString((byte) (bytes[9] & 0x7F)) + ByteConvertUtil.bytesToString(bytes[8]) : ByteConvertUtil.bytesToString(bytes[9]) + ByteConvertUtil.bytesToString(bytes[8]);
        int exp = Integer.parseInt(exponent, 16);
        //除以2的63次方
        BigDecimal mantissa = new BigDecimal(bigIntegerValue).divide(BigDecimal.valueOf(Math.pow(2, 63)), MathContext.DECIMAL128);
        //偏移16383
        BigDecimal power = BigDecimal.valueOf(2).pow((int) exp - 16383);
        //精度处理 去掉小数点后连续的0包括其以后不为0的小数
        BigDecimal result = power.multiply(mantissa).add(new BigDecimal("0.0000001")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
        return sign ? result.negate() : result;
    }

}
