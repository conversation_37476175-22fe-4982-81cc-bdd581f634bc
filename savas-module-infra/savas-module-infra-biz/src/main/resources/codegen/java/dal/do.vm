package ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName};

import lombok.*;
import java.util.*;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#end
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#end
#end
import com.baomidou.mybatisplus.annotation.*;
import ${BaseDOClassName};

/**
 * ${table.classComment} DO
 *
 * <AUTHOR>
 */
@TableName("${table.tableName.toLowerCase()}")
@KeySequence("${table.tableName.toLowerCase()}_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${table.className}DO extends BaseDO {

## 特殊：树表专属逻辑
#if ( $table.templateType == 2 )
    public static final Long ${treeParentColumn_javaField_underlineCase.toUpperCase()}_ROOT = 0L;

#end
#foreach ($column in $columns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.primaryKey})##处理主键
    @TableId#if (${column.javaType} == 'String')(type = IdType.INPUT)#end
    #end
    private ${column.javaType} ${column.javaField};
#end
#end

}