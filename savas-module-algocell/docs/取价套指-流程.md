### **1. 模板维护**
#### **(1) 套指标模板维护**
- **按专业维护**：根据不同专业（如建筑、电气、机械等）创建和管理表格模板。
- **模板功能**：支持将某些列配置为“要素列”（关键字段，可能用于数据匹配或分析）。
- **多模板支持**：某些专业可能需要多个模板，以适应不同的需求或场景。

#### **(2) 取价格模板维护**
- **按专业维护**：类似于套指标模板，价格模板也按专业进行管理和维护。
- **模板功能**：支持配置“要素列”，用于后续的价格匹配或识别。
- **多模板支持**：某些专业可能有多个价格模板。

---

### **2. 数据维护**
#### **(1) 维护知识库**
- **层级结构**：知识库采用多级标签体系：
  - 一级标签 → 二级标签 → 要素标签 → 术语(支持正则表达式)。
- **功能**：将知识库中的数据结构化，便于后续的条件识别和匹配。
#### **(2) 维护描述对象**
- **功能**：在知识库的一级标签下可以维护描述对象列表，每条描述对象可以选择多个二级标签和一个CBS类型

#### **(3) 维护匹配库**
##### **a. 指标匹配库（手动维护）**
- **内容**：将指标数据与标签和要素数据关联。
- **结构**：一条指标数据可对应多条标签，每条标签又关联多条要素数据。
- **操作**：手动维护，确保数据的准确性和关联性。

##### **b. 行业价格匹配库（手动+自动维护）**
- **内容**：将价格数据与标签和要素数据关联。
- **结构**：类似指标匹配库，一条价格数据可对应多条标签，每条标签关联多条要素数据。
- **操作**：支持手动维护和自动维护（可以通过价格库导入自动识别存入）。
- 数据量：十万级

##### **c. 企业价格匹配库**

- 内容：同行业
- 结构：同行业
- 操作：同行业
- 数据量：百万级

##### **d. 项目价格匹配库**

- 内容：同行业
- 结构：同行业
- 操作：同行业
- 数据量：万级

#### **(4) 维护价格库**
##### **a. 行业价格库**
- **流程**：
  1. **导入价格库**：从Excel文件导入价格数据。
  2. **选择专业**：根据专业选择对应的价格模板。
  3. **数据转换**：将导入的数据转换为价格模板的格式。
  4. **保存数据**：保存转换后的数据。
  5. **要素识别**：根据知识库中的术语，识别价格数据中的要素列。
  6. **存入匹配库**：将识别后的数据存入取价匹配库，用于后续匹配。
- 数据量：十万级
##### **b. 企业价格库**

- 流程：同行业
- 数据量：百万级

##### **c. 项目价格库**

- 流程：同行业
- 数据量：万级

---

### **3. 套指标**

- 注：区分专业,某些专业需要在套指标前或后进行特殊处理

- **流程**：
  1. **导入设计条件**：从Excel文件导入设计条件数据。
  2. **选择专业**：根据专业选择对应的指标模板。
  3. **数据转换**：将设计条件数据转换为指标模板格式。
  4. **条件识别**：利用知识库的术语识别设计条件中的关键信息。
  5. **要素匹配**：根据指标匹配库，匹配相应的要素数据。
  6. **获取指标数据**：最终输出匹配到的指标数据。

---

### **4. 取价格**

- 注：区分专业,某些专业需要在套指标前或后进行特殊处理

- **流程**：
  1. **导入设计条件**：从Excel文件导入设计条件数据。
  2. **选择专业**：根据专业选择对应的价格模板。
  3. **数据转换**：将设计条件数据转换为价格模板格式。
  4. **条件识别**：利用知识库的术语识别设计条件中的关键信息。
  5. **要素匹配**：根据行业、企业、项目价格匹配库，匹配相应的要素数据。 
  6. **获取价格**：最终输出在三个库中匹配到的价格数据。

---

### **总结**
核心功能包括：
- **模板管理**：为不同专业维护指标模板和价格模板，支持灵活配置。
- **数据管理**：通过知识库（术语）、匹配库（指标和价格）、价格库管理结构化数据。
- **数据处理**：通过导入Excel、模板转换、要素识别和匹配，完成指标提取和价格获取。

#### 前端部分:
1.工程量条件excel表导入，通过代码转换成指定的excel表格(标准清单表)，当代码识别规则对部分格式的条件不能完成识别时调用AI大模型识别。
2.价格库excel表导入，通过代码转换成指定的excel表格(标准价格表)，当代码识别规则对部分格式的条件不能完成识别时调用AI大模型识别。

#### 后端部分:
1.将"标准清单表"中的每条数据进行套取指标
1.1.通过"知识库"对每条数据进行关键字解析从而得到要素套取指标，如果因缺少要素没有取到指标，则调用AI大模型进行关键字识别匹配合适的要素，也对知识库进行了补充。
1.2.通过取到的指标获取"系数换算"
2.将"标准价格表"中的每条数据进行取价
2.1.通过"知识库"对每条数据进行关键字解析从而得到要素进行取价，如果因缺少要素没有取到指标，则调用AI大模型进行关键字识别匹配合适的要素，也对知识库进行了补充。
