@startuml
!define RECTANGLE class

package "表现层" {
  [前端应用] as Frontend
  [移动端应用] as Mobile
  note right of Frontend : 负责条件导入、识别、调整
}

package "接口层" {
  [API网关] as Gateway
  [认证服务] as Auth
}

package "应用层" {
  [项目管理服务] as ProjectService
  [组价中心服务] as PricingService
  [价格库管理服务] as LibraryService
  [系统管理服务] as SystemService
  note right of PricingService : 负责指标套取、价格计取
}

package "领域层" {
  [指标匹配引擎] as IndexEngine
  [价格匹配引擎] as PriceEngine
  [知识库引擎] as KnowledgeEngine
  [系数计算引擎] as CoefficientEngine
  note right of IndexEngine : 后端核心业务逻辑
}

package "基础设施层" {
  [数据访问层] as DataAccess
  [缓存服务] as Cache
  [文件存储] as FileStorage
  [外部接口] as ExternalAPI
}

package "数据层" {
  [业务数据库] as Database
  [缓存数据库] as Redis
  [文件系统] as FileSystem
}

Frontend --> Gateway : 条件数据传输
Mobile --> Gateway
Gateway --> Auth
Gateway --> ProjectService
Gateway --> PricingService : 指标价格请求
Gateway --> LibraryService
Gateway --> SystemService

PricingService --> IndexEngine : 指标套取
PricingService --> PriceEngine : 价格计取
PricingService --> CoefficientEngine : 系数计算
LibraryService --> KnowledgeEngine

IndexEngine --> DataAccess
PriceEngine --> DataAccess
KnowledgeEngine --> DataAccess
CoefficientEngine --> DataAccess

DataAccess --> Database
Cache --> Redis
FileStorage --> FileSystem
ExternalAPI --> [长城大模型] : 前端直接调用
@enduml
