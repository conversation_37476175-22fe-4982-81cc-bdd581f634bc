@startuml
skinparam packageStyle rectangle

package "表现层" {
  [前端应用] as Frontend
}

package "接口层" {
  [API网关] as Gateway
  [认证服务] as Auth
}

package "应用层" {
  [项目管理服务] as ProjectService
  [组价中心服务] as PricingService
  [价格库管理服务] as LibraryService
  [系统管理服务] as SystemService
  [知识补充服务] as AsyncService
}

package "领域层" {
  [指标匹配引擎] as IndexEngine
  [价格匹配引擎] as PriceEngine
  [知识库引擎] as KnowledgeEngine
  [系数计算引擎] as CoefficientEngine
  [CBS分类引擎] as CBSEngine
  [价格库转换引擎] as LibraryConvertEngine
}

package "基础设施层" {
  [数据访问层] as DataAccess
  [缓存服务] as Cache
  [文件存储] as FileStorage
  [外部接口] as ExternalAPI
  [消息队列] as MessageQueue
}

package "数据层" {
  [业务数据库] as Database
  [缓存数据库] as Redis
  [文件系统] as FileSystem
  [AI补充数据库] as AIDatabase
}

' ---- 层次连接 ----
Frontend --> Gateway
Gateway --> Auth
Gateway --> ProjectService
Gateway --> PricingService
Gateway --> LibraryService
Gateway --> SystemService
Gateway --> AsyncService

PricingService --> IndexEngine
PricingService --> PriceEngine
PricingService --> CBSEngine
PricingService --> CoefficientEngine
LibraryService --> LibraryConvertEngine
LibraryConvertEngine --> KnowledgeEngine

' ---- 基础设施访问 ----
IndexEngine --> DataAccess
PriceEngine --> DataAccess
CoefficientEngine --> DataAccess
KnowledgeEngine --> DataAccess
LibraryConvertEngine --> DataAccess

DataAccess --> Database
DataAccess --> AIDatabase
Cache --> Redis
FileStorage --> FileSystem
@enduml
