@startuml
!define RECTANGLE class

package "表现层" {
  [前端应用] as Frontend
}

package "接口层" {
  [认证服务] as Auth
  [API网关] as Gateway
}

package "应用层" {
  [项目管理服务] as ProjectService
  [组价中心服务] as PricingService
  [价格库管理服务] as LibraryService
  [系统管理服务] as SystemService
}

package "领域层" {
  [条件识别引擎] as RecognitionEngine
  [指标匹配引擎] as IndexEngine
  [价格匹配引擎] as PriceEngine
  [知识库引擎] as KnowledgeEngine
}

package "基础设施层" {
  [数据访问层] as DataAccess
  [缓存服务] as Cache
  [文件存储] as FileStorage
  [外部接口] as ExternalAPI
}

package "数据层" {
  [业务数据库] as Database
  [缓存数据库] as Redis
  [文件系统] as FileSystem
}

Frontend --> Auth
Auth --> Gateway
Gateway --> ProjectService
Gateway --> PricingService
Gateway --> LibraryService
Gateway --> SystemService

ProjectService --> RecognitionEngine
PricingService --> IndexEngine
PricingService --> PriceEngine
LibraryService --> KnowledgeEngine

RecognitionEngine --> DataAccess
IndexEngine --> DataAccess
PriceEngine --> DataAccess
KnowledgeEngine --> DataAccess

DataAccess --> Database
Cache --> Redis
FileStorage --> FileSystem
ExternalAPI --> [长城大模型]
@enduml
