@startuml
node "前端服务器" {
  [Vue.js应用] as <PERSON><PERSON><PERSON><PERSON>
  [<PERSON>in<PERSON>] as WebServer
}

node "应用服务器集群" {
  [Spring Boot应用1] as App1
  [Spring Boot应用2] as App2
  [Spring Boot应用N] as App<PERSON>
}

node "数据库服务器" {
  database "MySQL主库" as MasterDB
  database "MySQL从库" as SlaveDB
}

node "缓存服务器" {
  database "Redis集群" as RedisCluster
}

node "文件服务器" {
  [文件存储系统] as FileServer
}

cloud "外部服务" {
  [长城大模型API] as AIService
  [统一认证系统] as AuthSystem
}

WebServer --> App1
WebServer --> App2
WebServer --> AppN

App1 --> MasterDB
App2 --> MasterDB
AppN --> MasterDB

App1 --> SlaveDB
App2 --> SlaveDB
AppN --> SlaveDB

App1 --> RedisCluster
App2 --> RedisCluster
AppN --> RedisCluster

App1 --> FileServer
App2 --> FileServer
AppN --> FileServer

App1 --> AIService
App2 --> AIService
AppN --> AIService

App1 --> AuthSystem
App2 --> AuthSystem
AppN --> AuthSystem
@enduml
