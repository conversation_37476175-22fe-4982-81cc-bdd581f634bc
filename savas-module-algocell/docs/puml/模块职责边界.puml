@startuml
package "项目管理模块" {
  [项目生命周期管理] as ProjectLifecycle
  [任务分配管理] as TaskAssignment
  [项目配置管理] as ProjectConfig
}

package "组价中心模块" {
  [条件导入处理] as ConditionImport
  [智能识别引擎] as Recognition
  [指标套取引擎] as IndexMatching
  [价格计取引擎] as PriceMatching
  [结果输出处理] as ResultOutput
}

package "价格库管理模块" {
  [行业价格库] as IndustryLibrary
  [企业价格库] as EnterpriseLibrary
  [项目价格库] as ProjectLibrary
  [知识库管理] as KnowledgeBase
  [匹配库管理] as MatchingLibrary
}

package "系统管理模块" {
  [用户权限管理] as UserManagement
  [系统配置管理] as SystemConfig
  [日志审计管理] as AuditLog
}

ProjectLifecycle --> ProjectConfig
TaskAssignment --> ProjectLifecycle
ConditionImport --> ProjectConfig
Recognition --> KnowledgeBase
IndexMatching --> MatchingLibrary
PriceMatching --> IndustryLibrary
PriceMatching --> EnterpriseLibrary
PriceMatching --> ProjectLibrary
ResultOutput --> IndexMatching
ResultOutput --> PriceMatching
@enduml
