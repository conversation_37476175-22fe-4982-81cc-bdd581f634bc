@startuml
interface "项目管理接口" as IProjectManagement {
  +getProjectConfig(projectId): ProjectConfig
  +validateProjectPermission(userId, projectId): Boolean
  +getProjectSettings(projectId): ProjectSettings
}

interface "条件识别接口" as IRecognition {
  +codeRecognition(conditionData, specialty): RecognitionResult
  +aiRecognition(conditionData, specialty): RecognitionResult
  +adjustRecognitionResult(resultId, adjustments): Boolean
}

interface "指标匹配接口" as IIndexMatching {
  +matchIndex(recognitionResult): List<IndexMatch>
  +calculateCoefficient(indexMatch): CoefficientResult
  +getRecommendations(partialMatch): List<IndexRecommendation>
}

interface "价格匹配接口" as IPriceMatching {
  +matchPrice(indexResult, libraryType): List<PriceMatch>
  +calculateFinalPrice(priceMatch, adjustments): PriceResult
  +getPriceRecommendations(partialMatch): List<PriceRecommendation>
}

interface "知识库接口" as IKnowledgeBase {
  +getRecognitionRules(specialty): RecognitionRules
  +getIndexLibrary(specialty): IndexLibrary
  +getPriceLibrary(libraryType, specialty): PriceLibrary
  +updateKnowledgeBase(feedback): Boolean
}

class "组价中心服务" as PricingService
class "项目管理服务" as ProjectService
class "价格库管理服务" as LibraryService

PricingService --> IProjectManagement
PricingService --> IRecognition
PricingService --> IIndexMatching
PricingService --> IPriceMatching
PricingService --> IKnowledgeBase

ProjectService ..|> IProjectManagement
LibraryService ..|> IKnowledgeBase
@enduml
