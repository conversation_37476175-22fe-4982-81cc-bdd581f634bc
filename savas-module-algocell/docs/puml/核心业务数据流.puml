@startuml
participant "前端" as Frontend
participant "项目管理" as ProjectMgmt
participant "组价中心" as PricingCenter
participant "识别引擎" as RecognitionEngine
participant "匹配引擎" as MatchingEngine
participant "价格库" as PriceLibrary
participant "知识库" as KnowledgeBase

== 项目初始化阶段 ==
Frontend -> ProjectMgmt: 创建项目
ProjectMgmt -> ProjectMgmt: 生成项目配置
ProjectMgmt --> Frontend: 返回项目信息

== 条件导入阶段（前端处理）==
Frontend -> Frontend: 上传设计条件表
Frontend -> Frontend: 解析Excel文件
Frontend -> Frontend: 数据预处理和校验
Frontend -> ProjectMgmt: 保存原始数据

== 智能识别阶段（前端处理）==
Frontend -> KnowledgeBase: 获取识别规则
KnowledgeBase --> Frontend: 返回规则配置
Frontend -> Frontend: 执行代码识别
alt AI识别需求
  Frontend -> 长城大模型: 调用AI识别
  长城大模型 --> Frontend: 返回AI识别结果
end
Frontend -> Frontend: 识别结果调整
Frontend -> ProjectMgmt: 保存识别结果

== 指标匹配阶段（后端处理）==
Frontend -> PricingCenter: 提交识别结果，请求指标套取
PricingCenter -> KnowledgeBase: 获取指标库
KnowledgeBase --> PricingCenter: 返回指标数据
PricingCenter -> MatchingEngine: 执行指标匹配
MatchingEngine --> PricingCenter: 返回匹配结果
PricingCenter -> PricingCenter: 计算系数调整
PricingCenter --> Frontend: 返回套取结果

== 价格计取阶段（后端处理）==
Frontend -> PricingCenter: 请求价格计取
PricingCenter -> PriceLibrary: 按优先级查询价格库
note right: 项目库 > 企业库 > 行业库
PriceLibrary --> PricingCenter: 返回价格信息
PricingCenter -> MatchingEngine: 执行价格匹配
MatchingEngine --> PricingCenter: 返回价格结果
PricingCenter -> PricingCenter: 生成最终结果和CBS分类
PricingCenter --> Frontend: 返回计取结果
@enduml
