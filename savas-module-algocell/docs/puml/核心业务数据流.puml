@startuml
participant "前端" as Frontend
participant "项目管理" as ProjectMgmt
participant "组价中心" as PricingCenter
participant "识别引擎" as RecognitionEngine
participant "匹配引擎" as MatchingEngine
participant "价格库" as PriceLibrary
participant "知识库" as KnowledgeBase

== 项目初始化阶段 ==
Frontend -> ProjectMgmt: 创建项目
ProjectMgmt -> ProjectMgmt: 生成项目配置
ProjectMgmt --> Frontend: 返回项目信息

== 条件导入阶段 ==
Frontend -> PricingCenter: 上传设计条件表
PricingCenter -> PricingCenter: 解析Excel文件
PricingCenter -> PricingCenter: 存储原始数据
PricingCenter --> Frontend: 返回导入结果

== 智能识别阶段 ==
Frontend -> PricingCenter: 请求条件识别
PricingCenter -> KnowledgeBase: 获取识别规则
KnowledgeBase --> PricingCenter: 返回规则配置
PricingCenter -> RecognitionEngine: 执行识别处理
RecognitionEngine --> PricingCenter: 返回识别结果
PricingCenter -> PricingCenter: 存储识别数据
PricingCenter --> Frontend: 返回识别结果

== 指标匹配阶段 ==
Frontend -> PricingCenter: 请求指标套取
PricingCenter -> KnowledgeBase: 获取指标库
KnowledgeBase --> PricingCenter: 返回指标数据
PricingCenter -> MatchingEngine: 执行指标匹配
MatchingEngine --> PricingCenter: 返回匹配结果
PricingCenter -> PricingCenter: 计算系数调整
PricingCenter --> Frontend: 返回套取结果

== 价格计取阶段 ==
Frontend -> PricingCenter: 请求价格计取
PricingCenter -> PriceLibrary: 获取价格数据
PriceLibrary --> PricingCenter: 返回价格信息
PricingCenter -> MatchingEngine: 执行价格匹配
MatchingEngine --> PricingCenter: 返回价格结果
PricingCenter -> PricingCenter: 生成最终结果
PricingCenter --> Frontend: 返回计取结果
@enduml
