@startuml
package "项目管理API" {
  [POST /projects] as CreateProject
  [GET /projects/{id}] as GetProject
  [PUT /projects/{id}/settings] as UpdateSettings
}

package "组价中心API" {
  [POST /conditions/import] as ImportCondition
  [POST /conditions/{id}/recognize] as RecognizeCondition
  [POST /conditions/{id}/adjust] as AdjustResult
  [POST /index-matching/match] as MatchIndex
  [POST /price-matching/match] as MatchPrice
}

package "价格库管理API" {
  [GET /libraries] as GetLibraries
  [POST /libraries/import] as ImportLibrary
  [PUT /libraries/{id}/publish] as PublishLibrary
}

package "结果输出API" {
  [GET /results/{projectId}/export] as ExportResults
  [GET /results/{projectId}/preview] as PreviewResults
}
@enduml
