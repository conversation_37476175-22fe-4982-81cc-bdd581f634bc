@startuml
!define RECTANGLE class

package "前端层" {
  RECTANGLE Vue3 as Vue
  RECTANGLE ElementPlus as ElementUI
  RECTANGLE Axios as Axios
}

package "接口层" {
  RECTANGLE APIGateway as Gateway
  RECTANGLE Auth as Auth
  RECTANGLE RESTAPI as API
}

package "业务服务层" {
  RECTANGLE ProjectService as "项目管理服务"
  RECTANGLE PricingService as "组价中心服务"
  RECTANGLE DBService as "数据库管理服务"
  RECTANGLE ExportService as "结果输出服务"
  RECTANGLE SysService as "系统管理服务"
  RECTANGLE LogService as "日志与监控服务"
  RECTANGLE Scheduler as "任务调度服务"
  RECTANGLE Cache as "缓存服务(Redis)"
  RECTANGLE AIService as "AI识别/匹配服务"
}

package "数据存储层" {
  RECTANGLE MySQL as "关系数据库(MySQL)"
  RECTANGLE FileStore as "文件存储"
  RECTANGLE Redis as "缓存存储"
  RECTANGLE ES as "全文检索(ElasticSearch)"
}

Vue --> Axios
Axios --> Gateway
Gateway --> Auth
Gateway --> API

API --> ProjectService
API --> PricingService
API --> DBService
API --> ExportService
API --> SysService
API --> LogService

PricingService --> AIService
DBService --> MySQL
ProjectService --> MySQL
ExportService --> FileStore
LogService --> ES
Cache --> Redis
Scheduler --> ProjectService
Scheduler --> PricingService

@enduml
