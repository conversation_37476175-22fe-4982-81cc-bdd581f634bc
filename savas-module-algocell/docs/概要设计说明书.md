# 智能化概算编制系统概要设计说明书

## 1. 引言

### 1.1 编写目的
本文档描述了智能化概算编制系统的概要设计方案，明确系统的整体架构、模块划分、接口关系和关键技术选型，为详细设计和系统实现提供指导。
### 1.2 项目背景
智能化概算编制系统旨在实现以设计条件为数据源头，通过智能识别技术自动拆分识别设计条件，完成概算指标的套取、系数调整和价格计取，最终输出标准格式报表并与下游编制软件实现数据对接。

### 1.3 系统目标
- **智能化处理**：通过代码识别和AI大模型识别技术，自动处理各种格式的设计条件表
- **精准匹配**：基于知识库和匹配库，实现指标和价格的精准匹配
- **前后端分离**：前端负责条件导入和识别处理，后端负责指标套取和价格计取
- **标准化输出**：提供标准化的数据接口和报表输出

### 1.4 前后端职责划分
**前端职责**：
- 设计条件表导入：Excel文件上传、解析、格式转换
- 条件表代码识别：基于规则的自动识别和列映射
- 条件表大模型识别：调用AI服务进行智能识别
- 识别结果调整：可视化编辑、数据校验、结果确认

**后端职责**：
- 指标套取：基于知识库的要素匹配和指标计算
- 价格计取：多级价格库查询和价格匹配
- 系数换算：根据要素信息进行系数计算和调整
- 数据管理：知识库、匹配库、价格库的维护和管理

## 2. 系统总体架构

### 2.1 分层架构

```plantuml
@startuml

package "表现层" {
  [前端应用] as Frontend
  [移动端应用] as Mobile
  note right of Frontend : 负责条件导入、识别、调整
}

package "接口层" {
  [API网关] as Gateway
  [认证服务] as Auth
}

package "应用层" {
  [项目管理服务] as ProjectService
  [组价中心服务] as PricingService
  [价格库管理服务] as LibraryService
  [系统管理服务] as SystemService
  note right of PricingService : 负责指标套取、价格计取
}

package "领域层" {
  [指标匹配引擎] as IndexEngine
  [价格匹配引擎] as PriceEngine
  [知识库引擎] as KnowledgeEngine
  [系数计算引擎] as CoefficientEngine
  note right of IndexEngine : 后端核心业务逻辑
}

package "基础设施层" {
  [数据访问层] as DataAccess
  [缓存服务] as Cache
  [文件存储] as FileStorage
  [外部接口] as ExternalAPI
}

package "数据层" {
  [业务数据库] as Database
  [缓存数据库] as Redis
  [文件系统] as FileSystem
}

Frontend --> Gateway : 条件数据传输
Mobile --> Gateway
Gateway --> Auth
Gateway --> ProjectService
Gateway --> PricingService : 指标价格请求
Gateway --> LibraryService
Gateway --> SystemService

PricingService --> IndexEngine : 指标套取
PricingService --> PriceEngine : 价格计取
PricingService --> CoefficientEngine : 系数计算
LibraryService --> KnowledgeEngine

IndexEngine --> DataAccess
PriceEngine --> DataAccess
KnowledgeEngine --> DataAccess
CoefficientEngine --> DataAccess

DataAccess --> Database
Cache --> Redis
FileStorage --> FileSystem
ExternalAPI --> [长城大模型] : 前端直接调用
@enduml
```

## 3. 技术架构

### 3.1 核心技术栈

#### 3.1.1 前端技术栈
- **Vue.js 3.x**：现代化的渐进式JavaScript框架，具备良好的组件化和响应式特性


#### 3.1.2 后端技术栈
- **Spring Boot 2.7.18**：成熟的Java企业级开发框架，生态完善
- **MyBatis Plus**：增强版MyBatis，提供代码生成和通用CRUD功能
- **MySQL 8.0**：成熟稳定的关系型数据库，支持JSON字段和全文索引
- **Redis**：高性能内存数据库，用于缓存和会话存储
- **Spring Security**：企业级安全框架，提供认证和授权功能
- **MinIO**：高性能的对象存储框架，提供分布式存储和S3兼容的API接口。

## 4. 功能模块划分

### 4.1 模块职责边界

```plantuml
@startuml

package "项目管理模块" {
  [项目生命周期管理] as ProjectLifecycle
  [任务分配管理] as TaskAssignment
  [项目配置管理] as ProjectConfig
}

package "组价中心模块" {
  package "前端处理" {
    [条件导入处理] as ConditionImport
    [智能识别引擎] as Recognition
    [识别结果调整] as ResultAdjust
  }
  package "后端处理" {
    [指标套取引擎] as IndexMatching
    [价格计取引擎] as PriceMatching
    [系数换算引擎] as CoefficientCalc
    [结果输出处理] as ResultOutput
  }
}

package "价格库管理模块" {
  [行业价格库] as IndustryLibrary
  [企业价格库] as EnterpriseLibrary
  [项目价格库] as ProjectLibrary
  [知识库管理] as KnowledgeBase
  [匹配库管理] as MatchingLibrary
}

package "系统管理模块" {
  [用户权限管理] as UserManagement
  [系统配置管理] as SystemConfig
  [日志审计管理] as AuditLog
}

ProjectLifecycle --> ProjectConfig
TaskAssignment --> ProjectLifecycle
ConditionImport --> ProjectConfig : 获取项目配置
Recognition --> KnowledgeBase : 前端调用识别规则
ResultAdjust --> Recognition : 调整识别结果
IndexMatching --> MatchingLibrary : 后端指标匹配
IndexMatching --> ResultAdjust : 基于调整后数据
PriceMatching --> IndustryLibrary
PriceMatching --> EnterpriseLibrary
PriceMatching --> ProjectLibrary
CoefficientCalc --> IndexMatching : 系数计算
ResultOutput --> PriceMatching
ResultOutput --> CoefficientCalc
@enduml
```

### 4.2 核心模块设计

#### 4.2.1 项目管理模块
**功能边界**：
- 项目全生命周期管理（创建、配置、执行）
- 项目结构管理（分类、单项、专业划分）
- 任务分配和人员管理
- 项目参数配置（铜价、防腐工艺、运输参数等）

#### 4.2.2 组价中心模块
**前端功能边界**：
- 设计条件表的导入和解析（Excel文件上传、格式识别）
- 条件数据的智能识别和转换（代码识别、AI识别）
- 识别结果的可视化调整（数据编辑、格式校验）
- 识别结果的确认和提交（数据验证、状态管理）

**后端功能边界**：
- 概算指标的自动套取和匹配（基于知识库的要素匹配）
- 价格数据的计取和调整（多级价格库查询匹配）
- 系数换算和价格计算（根据要素进行系数调整）
- 结果数据的汇总和输出（CBS分类、报表生成）

**前端核心子模块**：
- **文件处理子模块**：Excel文件上传、解析、预览
- **代码识别子模块**：基于规则和模板的自动识别
- **AI识别子模块**：调用长城大模型进行智能识别
- **结果调整子模块**：可视化编辑界面和数据校验

**后端核心子模块**：
- **知识库管理子模块**：术语库、标签体系
- **匹配引擎子模块**：知识库与匹配库**运行时**管理与匹配
- **匹配库管理子模块**：匹配库的访问和维护
- **指标匹配子模块**：各专业指标匹配预处理与结果生成
- **价格匹配子模块**：各专业价格匹配预处理与结果生成
- **系数换算子模块**：根据要素信息进行系数换算
- **输出处理子模块**：数据汇总、CBS分类、报表生成

#### 4.2.3 价格库管理模块
**功能边界**：
- 三级价格库的创建、维护
- 知识库的构建和优化
- 匹配库的维护和更新
- 价格数据的导入、校验

**数据层次**：
- **行业价格库**：基础价格数据，来源于权威发布
- **企业价格库**：在行业库基础上的企业定制
- **项目价格库**：针对特定项目的价格定制

#### 4.2.4 系统管理模块
**功能边界**：
- 用户账号和角色权限管理
- 系统参数和业务规则配置
- 操作日志和审计跟踪
- 系统监控和性能管理

## 5. 接口设计概要

### 5.1 模块间接口关系

#### 5.1.1 项目管理接口
**接口功能**：为其他模块提供项目配置和权限验证服务
- `getProjectConfig(projectId)`: 获取项目配置信息
- `validateProjectPermission(userId, projectId)`: 验证用户项目权限
- `getProjectSettings(projectId)`: 获取项目参数设置

#### 5.1.2 前端识别接口（前端内部）
**接口功能**：前端内部的识别处理接口
- `codeRecognition(conditionData, specialty)`: 执行代码识别
- `aiRecognition(conditionData, specialty)`: 调用AI识别
- `adjustRecognitionResult(resultId, adjustments)`: 调整识别结果

#### 5.1.3 指标匹配接口（后端提供）
**接口功能**：后端提供的指标套取服务
- `matchIndex(recognitionResult)`: 执行指标匹配
- `calculateCoefficient(indexMatch)`: 计算系数调整
- `getRecommendations(partialMatch)`: 获取推荐指标

#### 5.1.4 价格匹配接口（后端提供）
**接口功能**：后端提供的价格计取服务
- `matchPrice(indexResult, libraryType)`: 执行价格匹配
- `calculateFinalPrice(priceMatch, adjustments)`: 计算最终价格
- `getPriceRecommendations(partialMatch)`: 获取推荐价格

#### 5.1.5 知识库接口
**接口功能**：提供知识库和匹配库数据服务
- `getRecognitionRules(specialty)`: 获取识别规则（前端调用）
- `getIndexLibrary(specialty)`: 获取指标库数据（后端调用）
- `getPriceLibrary(libraryType, specialty)`: 获取价格库数据（后端调用）
- `updateKnowledgeBase(feedback)`: 更新知识库（学习反馈）

#### 5.1.6 接口调用关系
**前端调用**：
- 项目管理接口 → 获取项目配置
- 知识库接口 → 获取识别规则
- 长城大模型接口 → AI识别服务

**后端调用**：
- 项目管理接口 → 验证权限和获取配置
- 知识库接口 → 获取指标库和价格库
- 指标匹配接口 → 内部服务调用
- 价格匹配接口 → 内部服务调用

### 5.2 外部接口设计

#### 5.2.1 前端API接口
**RESTful API设计原则**：
- 资源导向的URL设计
- 标准HTTP方法语义
- 统一的响应格式
- 完善的错误处理

#### 5.2.2 项目管理API
**基础路径**：`/api/v1/projects`

**接口列表**：
- **POST /api/v1/projects**
  - 功能：创建新项目
  - 参数：项目名称、描述、专业配置等
  - 响应：项目ID和基本信息

- **GET /api/v1/projects/{id}**
  - 功能：获取项目详情
  - 参数：项目ID
  - 响应：完整项目信息和配置

- **PUT /api/v1/projects/{id}/settings**
  - 功能：更新项目设置
  - 参数：项目ID、配置参数
  - 响应：更新结果确认

#### 5.2.3 组价中心API（后端提供）
**基础路径**：`/api/v1/pricing`

**指标套取接口**：
- **POST /api/v1/pricing/index-matching/match**
  - 功能：执行指标匹配
  - 参数：识别结果数据、专业类型
  - 响应：指标匹配结果列表

- **POST /api/v1/pricing/index-matching/coefficient**
  - 功能：计算系数调整
  - 参数：指标匹配ID、要素信息
  - 响应：系数计算结果

**价格计取接口**：
- **POST /api/v1/pricing/price-matching/match**
  - 功能：执行价格匹配
  - 参数：指标结果、价格库类型
  - 响应：价格匹配结果列表

- **POST /api/v1/pricing/price-matching/calculate**
  - 功能：计算最终价格
  - 参数：价格匹配ID、调整参数
  - 响应：最终价格计算结果

#### 5.2.4 价格库管理API
**基础路径**：`/api/v1/libraries`

**接口列表**：
- **GET /api/v1/libraries**
  - 功能：获取价格库列表
  - 参数：库类型、专业筛选
  - 响应：价格库基本信息列表

- **POST /api/v1/libraries/import**
  - 功能：导入价格库数据
  - 参数：Excel文件、库类型、专业
  - 响应：导入结果和统计信息

- **PUT /api/v1/libraries/{id}/publish**
  - 功能：发布价格库版本
  - 参数：库ID、版本信息
  - 响应：发布结果确认

#### 5.2.5 结果输出API
**基础路径**：`/api/v1/results`

**接口列表**：
- **GET /api/v1/results/{projectId}/export**
  - 功能：导出项目结果
  - 参数：项目ID、导出格式
  - 响应：文件下载链接

- **GET /api/v1/results/{projectId}/preview**
  - 功能：预览项目结果
  - 参数：项目ID、预览类型
  - 响应：结果数据预览

#### 5.2.6 外部系统接口

**长城大模型接口**（前端直接调用）：
- **功能**：智能识别设计条件表内容
- **输入**：标准模板、设计条件文本
- **输出**：结构化的识别结果JSON数据
- **安全**：API密钥认证

**统一认证系统接口**：
- **接口类型**：OAuth 2.0 / JWT Token
- **功能**：用户身份认证和授权
- **Token管理**：访问令牌和刷新令牌机制
- **权限控制**：基于角色的访问控制（RBAC）

## 6. 数据流设计

### 6.1 核心业务数据流

```plantuml
@startuml
participant "前端" as Frontend
participant "项目管理" as ProjectMgmt
participant "组价中心" as PricingCenter
participant "识别引擎" as RecognitionEngine
participant "匹配引擎" as MatchingEngine
participant "价格库" as PriceLibrary
participant "指标库" as KnowledgeBase

== 项目初始化阶段 ==
Frontend -> ProjectMgmt: 创建项目
ProjectMgmt -> ProjectMgmt: 生成项目配置
ProjectMgmt --> Frontend: 返回项目信息

== 条件导入阶段（前端处理）==
Frontend -> Frontend: 上传设计条件表
Frontend -> Frontend: 解析Excel文件
Frontend -> Frontend: 数据预处理和校验
Frontend -> ProjectMgmt: 保存原始数据

== 智能识别阶段（前端处理）==
Frontend -> KnowledgeBase: 获取识别规则
KnowledgeBase --> Frontend: 返回规则配置
Frontend -> Frontend: 执行代码识别
alt AI识别需求
  Frontend -> 长城大模型: 调用AI识别
  长城大模型 --> Frontend: 返回AI识别结果
end
Frontend -> Frontend: 识别结果调整
Frontend -> ProjectMgmt: 保存识别结果

== 指标匹配阶段（后端处理）==
Frontend -> PricingCenter: 提交识别结果，请求指标套取
PricingCenter -> KnowledgeBase: 获取指标库
KnowledgeBase --> PricingCenter: 返回指标数据
PricingCenter -> MatchingEngine: 执行指标匹配
MatchingEngine --> PricingCenter: 返回匹配结果
PricingCenter -> PricingCenter: 计算系数调整
PricingCenter --> Frontend: 返回套取结果

== 价格计取阶段（后端处理）==
Frontend -> PricingCenter: 请求价格计取
PricingCenter -> PriceLibrary: 同时查询价格库
note right: 项目库, 企业库, 行业库
PriceLibrary --> PricingCenter: 返回价格信息
PricingCenter -> MatchingEngine: 执行价格匹配
MatchingEngine --> PricingCenter: 返回价格结果
PricingCenter -> PricingCenter: 生成最终结果和CBS分类
PricingCenter --> Frontend: 返回计取结果
@enduml
```

### 6.2 数据存储策略

#### 6.2.1 数据分类
**业务数据**：
- 项目数据：项目基本信息、配置参数、任务分配
- 条件数据：原始设计条件数据、专业通用模板、识别结果
- 匹配数据：指标匹配结果、系数换算、价格匹配结果、CBS分类

**配置数据**：
- 知识库数据：标签体系、术语库、识别规则
- 价格库数据：行业库、企业库、项目库
- 匹配库数据：指标匹配库、价格匹配库
- 系统配置：用户权限、业务参数、模板配置

#### 6.2.2 数据流转路径

**前端处理流程**：
1. **导入流程**：Excel文件上传 → 前端解析 → 数据预处理 → 格式校验
2. **识别流程**：原始数据 → 代码识别/AI识别 → 识别结果 → 可视化调整 → 确认数据
3. **提交流程**：确认数据 → 数据验证 → 提交后端 → 状态跟踪

**后端处理流程**：
1. **接收流程**：接收前端数据 → 数据验证 → 存储处理
2. **匹配流程**：要素提取 → 指标匹配 → 系数计算 → 价格匹配 → 结果生成
3. **输出流程**：匹配结果 → CBS分类 → 数据汇总 → 格式转换 → 接口输出

## 7. 关键业务流程

### 7.1 智能识别流程控制（前端执行）

```plantuml
@startuml
start

:前端接收设计条件表;
note right: 用户上传Excel文件

:前端解析Excel文件;
:数据预处理和校验;

:前端从后端获取专业通用模板;
note right: 根据专业类型获取标准模板

:前端执行代码识别;
note right: 基于规则将条件表转换为通用模板格式

if (代码识别是否成功?) then (成功)
  :生成识别结果;
else (失败或部分失败)
  :前端调用长城大模型API;
  note right: 对无法识别的部分进行AI识别
  :前端解析AI响应;
  :合并代码识别和AI识别结果;
  :生成完整识别结果;
endif

:前端展示识别结果;
note right: 按专业通用模板格式展示

:用户确认和调整;
if (需要调整?) then (是)
  :前端提供编辑界面;
  :用户手动调整数据;
  :前端验证调整结果;
else (否)
endif

:前端确认最终结果;
note right: 确保符合专业通用模板格式
:提交数据到后端;

stop
@enduml
```

### 7.2 通用匹配流程控制（后端执行）

```plantuml
@startuml
start

:后端接收前端识别结果;
note right: 前端提交的专业通用模板数据（批量）

:批量处理每条数据;
note right: 处理每条设计条件数据

:后端通过知识库识别设计条件;
note right: 基于知识库术语识别关键信息

:后端提取要素信息;
note right: 解析名称、规格、单位等关键要素

if (匹配类型?) then (指标匹配)
  :后端查询指标匹配库;
  note right: 获取指标库数据
  :执行指标匹配算法;

  if (匹配结果?) then (完全匹配)
    :生成匹配度排序列表;
    note right: 按匹配分从高到低排序
  else (部分匹配)
    :请求AI大模型识别缺失要素;
    note right: 调用AI识别缺失的要素信息
    :生成匹配度排序列表;
    note right: 标记AI补充的匹配项
  endif
  
  :系数调整;
  :执行CBS分类;
  :保存指标匹配结果;

else (价格匹配)
  :后端检查设计条件价格;
  if (包含价格?) then (是)
    :使用设计条件价格;
    note right: 直接使用Excel中的价格
  else (否)
    :查询所有价格库;
    note right: 项目价格库、 企业价格库、 行业价格库
    :执行价格匹配算法;

    if (匹配结果?) then (完全匹配)
      :生成匹配度排序列表;
      note right: 按匹配分从高到低排序
    else (部分匹配)
      :请求AI大模型识别缺失要素;
      note right: 调用AI识别缺失的要素信息
      :生成匹配度排序列表;
      note right: 标记AI补充的匹配项
    endif
  endif

  :应用价格调整;
  note right: 根据系数和参数调整
  :计算最终价格;
  :保存价格计取结果;
endif

:处理下一条数据;
note right: 继续批量处理

:返回所有匹配结果给前端;
note right: 包含匹配度排序列表和AI标记

:用户确认匹配结果;
note right: 用户需要确认(匹配分)没有满分/AI补充的结果

:异步处理AI补充数据;
note right: 后台异步执行以下操作

fork
  :记录AI补充数据到数据库;
fork again
  :管理员审核AI补充结果;
  if (审核结果?) then (通过)
    :补充到知识库;
  else (拒绝)
    :记录审核拒绝原因;
  endif
fork again
  :为部分匹配数据增加权重评分;
  note right: 基于用户确认结果调整权重评分
end fork

stop
@enduml
```

### 7.3 决策点设计

#### 7.3.1 识别方式决策（前端自动决策）
**决策逻辑**：
- 首先尝试代码识别：基于专业通用模板进行规则匹配
- 代码识别失败时自动调用AI识别：对无法识别的部分进行智能识别
- 无需用户手动选择，系统自动判断和切换

**决策因素**：
- 代码识别的成功率和覆盖度
- 表格格式的复杂程度
- 专业模板的匹配程度

#### 7.3.2 通用匹配策略决策（后端决策）
**完全匹配条件**：
- 匹配库命中所有要素的数据项
- 匹配度达到100%

**人工干预条件**：
- 知识库无法识别关键要素
- 匹配度低于100%
- 存在多个匹配分/权重分相同的匹配结果
- 用户确认结果中存在AI补充项

#### 7.3.3 匹配类型决策
**指标匹配决策**：
- 基于识别的设计条件进行和匹配库匹配
- 优先完全匹配，其次部分匹配
- 支持系数换算和调整

**价格匹配决策**：
- 优先检查设计条件中是否包含价格
- 查询全部价格库（项目库, 企业库, 行业库）
- 基于相同的要素匹配逻辑进行价格匹配

#### 7.3.4 前后端协作决策
**前端决策范围**：
- 专业通用模板的获取和应用
- 代码识别与AI识别补充
- 识别结果的调整和确认
- 用户交互和数据展示

**后端决策范围**：
- 知识要素识别和提取
- 指标匹配和价格匹配
- 系数调整和价格计算
- CBS分类和结果输出

## 8. 非功能性需求设计

### 8.1 性能设计

#### 8.1.1 性能指标
- **响应时间**：系统平均响应时间 ≤ 5秒
- **并发处理**：支持100个并发用户同时操作
- **文件处理**：单个Excel文件处理时间 ≤ 10分钟（50万字符以内）
- **数据吞吐**：支持单次处理5000万字符的文件

### 8.3 安全设计

#### 8.3.1 认证授权
**认证机制**：
- 集成统一认证系统
- Spring Security & Token管理
- 会话超时控制

**授权控制**：
- 基于角色的访问控制
- 细粒度权限控制
- 数据权限隔离
- API访问控制
