# 智能化概算编制系统概要设计说明书

## 1. 引言

### 1.1 编写目的
本文档描述了智能化概算编制系统的概要设计方案，明确系统的整体架构、模块划分、接口关系和关键技术选型，为详细设计和系统实现提供指导。
### 1.2 项目背景
智能化概算编制系统旨在实现以设计条件为数据源头，通过智能识别技术自动拆分识别设计条件，完成概算指标的套取、系数调整和价格计取，最终输出标准格式报表并与下游编制软件实现数据对接。

### 1.3 系统目标
- **智能化处理**：通过代码识别和AI大模型识别技术，自动处理各种格式的设计条件表
- **精准匹配**：基于知识库和匹配库，实现指标和价格的精准匹配
- **标准化输出**：提供标准化的数据接口和报表输出

## 2. 系统总体架构

### 2.1 分层架构

```plantuml
@startuml
!define RECTANGLE class

package "表现层" {
  [前端应用] as Frontend
  [移动端应用] as Mobile
}

package "接口层" {
  [API网关] as Gateway
  [认证服务] as Auth
}

package "应用层" {
  [项目管理服务] as ProjectService
  [组价中心服务] as PricingService
  [价格库管理服务] as LibraryService
  [系统管理服务] as SystemService
}

package "领域层" {
  [条件识别引擎] as RecognitionEngine
  [指标匹配引擎] as IndexEngine
  [价格匹配引擎] as PriceEngine
  [知识库引擎] as KnowledgeEngine
}

package "基础设施层" {
  [数据访问层] as DataAccess
  [缓存服务] as Cache
  [文件存储] as FileStorage
  [外部接口] as ExternalAPI
}

package "数据层" {
  [业务数据库] as Database
  [缓存数据库] as Redis
  [文件系统] as FileSystem
}

Frontend --> Gateway
Mobile --> Gateway
Gateway --> Auth
Gateway --> ProjectService
Gateway --> PricingService
Gateway --> LibraryService
Gateway --> SystemService

ProjectService --> RecognitionEngine
PricingService --> IndexEngine
PricingService --> PriceEngine
LibraryService --> KnowledgeEngine

RecognitionEngine --> DataAccess
IndexEngine --> DataAccess
PriceEngine --> DataAccess
KnowledgeEngine --> DataAccess

DataAccess --> Database
Cache --> Redis
FileStorage --> FileSystem
ExternalAPI --> [长城大模型]
@enduml
```

## 3. 技术架构

### 3.1 核心技术栈

#### 3.1.1 前端技术栈
- **Vue.js 3.x**：现代化的渐进式JavaScript框架，具备良好的组件化和响应式特性


#### 3.1.2 后端技术栈
- **Spring Boot 2.7.18**：成熟的Java企业级开发框架，生态完善
- **MyBatis Plus**：增强版MyBatis，提供代码生成和通用CRUD功能
- **MySQL 8.0**：成熟稳定的关系型数据库，支持JSON字段和全文索引
- **Redis**：高性能内存数据库，用于缓存和会话存储
- **Spring Security**：企业级安全框架，提供认证和授权功能
- **MinIO**：高性能的对象存储框架，提供分布式存储和S3兼容的API接口。

## 4. 功能模块划分

### 4.1 模块职责边界

```plantuml
@startuml
package "项目管理模块" {
  [项目生命周期管理] as ProjectLifecycle
  [任务分配管理] as TaskAssignment
  [项目配置管理] as ProjectConfig
}

package "组价中心模块" {
  [条件导入处理] as ConditionImport
  [智能识别引擎] as Recognition
  [指标套取引擎] as IndexMatching
  [价格计取引擎] as PriceMatching
  [结果输出处理] as ResultOutput
}

package "价格库管理模块" {
  [行业价格库] as IndustryLibrary
  [企业价格库] as EnterpriseLibrary
  [项目价格库] as ProjectLibrary
  [知识库管理] as KnowledgeBase
  [匹配库管理] as MatchingLibrary
}

package "系统管理模块" {
  [用户权限管理] as UserManagement
  [系统配置管理] as SystemConfig
  [日志审计管理] as AuditLog
}

ProjectLifecycle --> ProjectConfig
TaskAssignment --> ProjectLifecycle
ConditionImport --> ProjectConfig
Recognition --> KnowledgeBase
IndexMatching --> MatchingLibrary
PriceMatching --> IndustryLibrary
PriceMatching --> EnterpriseLibrary
PriceMatching --> ProjectLibrary
ResultOutput --> IndexMatching
ResultOutput --> PriceMatching
@enduml
```

### 4.2 核心模块设计

#### 4.2.1 项目管理模块
**功能边界**：
- 项目全生命周期管理（创建、配置、执行）
- 项目结构管理（分类、单项、专业划分）
- 任务分配和人员管理
- 项目参数配置（铜价、防腐工艺、运输参数等）

**对外接口**：
- 为组价中心提供项目配置信息
- 为价格库管理提供项目价格库关联
- 为系统管理提供项目权限控制

#### 4.2.2 组价中心模块
**功能边界**：
- 设计条件表的导入和解析
- 条件数据的智能识别和转换
- 概算指标的自动套取和匹配
- 价格数据的计取和调整
- 结果数据的汇总和输出

**核心子模块**：
- **条件处理子模块**：负责Excel文件解析、数据清洗、格式转换
- **识别引擎子模块**：集成代码识别和AI识别能力
- **匹配引擎子模块**：实现指标匹配和价格匹配算法
- **输出处理子模块**：生成标准格式报表和API数据

#### 4.2.3 价格库管理模块
**功能边界**：
- 三级价格库的创建、维护和版本管理
- 知识库的构建和优化
- 匹配库的维护和更新
- 价格数据的导入、校验和发布

**数据层次**：
- **行业价格库**：基础价格数据，来源于权威发布
- **企业价格库**：在行业库基础上的企业定制
- **项目价格库**：针对特定项目的价格定制

#### 4.2.4 系统管理模块
**功能边界**：
- 用户账号和角色权限管理
- 系统参数和业务规则配置
- 操作日志和审计跟踪
- 系统监控和性能管理

## 5. 接口设计概要

### 5.1 模块间接口关系

```plantuml
@startuml
interface "项目管理接口" as IProjectManagement {
  +getProjectConfig(projectId): ProjectConfig
  +validateProjectPermission(userId, projectId): Boolean
  +getProjectSettings(projectId): ProjectSettings
}

interface "条件识别接口" as IRecognition {
  +codeRecognition(conditionData, specialty): RecognitionResult
  +aiRecognition(conditionData, specialty): RecognitionResult
  +adjustRecognitionResult(resultId, adjustments): Boolean
}

interface "指标匹配接口" as IIndexMatching {
  +matchIndex(recognitionResult): List<IndexMatch>
  +calculateCoefficient(indexMatch): CoefficientResult
  +getRecommendations(partialMatch): List<IndexRecommendation>
}

interface "价格匹配接口" as IPriceMatching {
  +matchPrice(indexResult, libraryType): List<PriceMatch>
  +calculateFinalPrice(priceMatch, adjustments): PriceResult
  +getPriceRecommendations(partialMatch): List<PriceRecommendation>
}

interface "知识库接口" as IKnowledgeBase {
  +getRecognitionRules(specialty): RecognitionRules
  +getIndexLibrary(specialty): IndexLibrary
  +getPriceLibrary(libraryType, specialty): PriceLibrary
  +updateKnowledgeBase(feedback): Boolean
}

class "组价中心服务" as PricingService
class "项目管理服务" as ProjectService
class "价格库管理服务" as LibraryService

PricingService --> IProjectManagement
PricingService --> IRecognition
PricingService --> IIndexMatching
PricingService --> IPriceMatching
PricingService --> IKnowledgeBase

ProjectService ..|> IProjectManagement
LibraryService ..|> IKnowledgeBase
@enduml
```

### 5.2 外部接口设计

#### 5.2.1 前端API接口
**RESTful API设计原则**：
- 资源导向的URL设计
- 标准HTTP方法语义
- 统一的响应格式
- 完善的错误处理

**核心API分组**：
```plantuml
@startuml
package "项目管理API" {
  [POST /projects] as CreateProject
  [GET /projects/{id}] as GetProject
  [PUT /projects/{id}/settings] as UpdateSettings
}

package "组价中心API" {
  [POST /conditions/import] as ImportCondition
  [POST /conditions/{id}/recognize] as RecognizeCondition
  [POST /conditions/{id}/adjust] as AdjustResult
  [POST /index-matching/match] as MatchIndex
  [POST /price-matching/match] as MatchPrice
}

package "价格库管理API" {
  [GET /libraries] as GetLibraries
  [POST /libraries/import] as ImportLibrary
  [PUT /libraries/{id}/publish] as PublishLibrary
}

package "结果输出API" {
  [GET /results/{projectId}/export] as ExportResults
  [GET /results/{projectId}/preview] as PreviewResults
}
@enduml
```

#### 5.2.2 外部系统接口
**长城大模型接口**：
- 接口类型：HTTP REST API

**统一认证系统接口**：
- Token管理：未知

## 6. 数据流设计

### 6.1 核心业务数据流

```plantuml
@startuml
participant "前端" as Frontend
participant "项目管理" as ProjectMgmt
participant "组价中心" as PricingCenter
participant "识别引擎" as RecognitionEngine
participant "匹配引擎" as MatchingEngine
participant "价格库" as PriceLibrary
participant "知识库" as KnowledgeBase

== 项目初始化阶段 ==
Frontend -> ProjectMgmt: 创建项目
ProjectMgmt -> ProjectMgmt: 生成项目配置
ProjectMgmt --> Frontend: 返回项目信息

== 条件导入阶段 ==
Frontend -> PricingCenter: 上传设计条件表
PricingCenter -> PricingCenter: 解析Excel文件
PricingCenter -> PricingCenter: 存储原始数据
PricingCenter --> Frontend: 返回导入结果

== 智能识别阶段 ==
Frontend -> PricingCenter: 请求条件识别
PricingCenter -> KnowledgeBase: 获取识别规则
KnowledgeBase --> PricingCenter: 返回规则配置
PricingCenter -> RecognitionEngine: 执行识别处理
RecognitionEngine --> PricingCenter: 返回识别结果
PricingCenter -> PricingCenter: 存储识别数据
PricingCenter --> Frontend: 返回识别结果

== 指标匹配阶段 ==
Frontend -> PricingCenter: 请求指标套取
PricingCenter -> KnowledgeBase: 获取指标库
KnowledgeBase --> PricingCenter: 返回指标数据
PricingCenter -> MatchingEngine: 执行指标匹配
MatchingEngine --> PricingCenter: 返回匹配结果
PricingCenter -> PricingCenter: 计算系数调整
PricingCenter --> Frontend: 返回套取结果

== 价格计取阶段 ==
Frontend -> PricingCenter: 请求价格计取
PricingCenter -> PriceLibrary: 获取价格数据
PriceLibrary --> PricingCenter: 返回价格信息
PricingCenter -> MatchingEngine: 执行价格匹配
MatchingEngine --> PricingCenter: 返回价格结果
PricingCenter -> PricingCenter: 生成最终结果
PricingCenter --> Frontend: 返回计取结果
@enduml
```

### 6.2 数据存储策略

#### 6.2.1 数据分类
**业务数据**：
- 项目数据：项目基本信息、配置参数、任务分配
- 条件数据：原始Excel数据、识别结果、调整记录
- 匹配数据：指标匹配结果、价格匹配结果、CBS分类

**配置数据**：
- 知识库数据：标签体系、术语库、识别规则
- 价格库数据：三级价格库、价格历史版本
- 系统配置：用户权限、业务参数、模板配置

**临时数据**：
- 文件上传：Excel文件、导出文件
- 缓存数据：热点查询结果、会话信息
- 日志数据：操作日志、错误日志、性能日志

#### 6.2.2 数据流转路径
1. **导入流程**：Excel文件 → 文件解析 → 原始数据存储 → 数据校验
2. **识别流程**：原始数据 → 识别引擎 → 识别结果 → 结果调整 → 确认数据
3. **匹配流程**：确认数据 → 要素提取 → 匹配计算 → 匹配结果 → 人工确认
4. **输出流程**：匹配结果 → 数据汇总 → 格式转换 → 文件生成 → 接口输出

## 7. 关键业务流程

### 7.1 智能识别流程控制

```plantuml
@startuml
start

:接收设计条件表;

:选择识别方式;
if (识别方式?) then (代码识别)
  :获取识别规则;
  :执行模板匹配;
  :生成识别结果;
else (AI识别)
  :获取知识库;
  :调用大模型API;
  :解析AI响应;
  :生成识别结果;
endif

:展示识别结果;

:用户确认;
if (需要调整?) then (是)
  :手动调整数据;
  :验证调整结果;
  :保存调整记录;
else (否)
endif

:确认最终结果;

stop
@enduml
```

### 7.2 指标匹配流程控制

```plantuml
@startuml
start

:获取识别结果;

:提取要素信息;

:查询指标库;

:执行匹配算法;

if (匹配结果?) then (完全匹配)
  :自动套取指标;
  :计算系数调整;
elseif (部分匹配) then
  :生成推荐列表;
  :用户选择指标;
  :计算系数调整;
else (无匹配)
  :提示无匹配结果;
  :用户手动选择;
  :记录人工选择;
endif

:保存匹配结果;

:更新学习样本;

stop
@enduml
```

### 7.3 价格计取流程控制

```plantuml
@startuml
start

:获取指标匹配结果;

:检查设计条件价格;
if (包含价格?) then (是)
  :使用设计条件价格;
else (否)
  :按优先级查询价格库;
  note right: 项目价格库 > 企业价格库 > 行业价格库

  :执行价格匹配;

  if (匹配结果?) then (完全匹配)
    :自动选择价格;
  elseif (部分匹配) then
    :生成价格推荐;
    :用户选择价格;
  else (无匹配)
    :用户手动输入;
  endif
endif

:应用价格调整;

:计算最终价格;

:执行CBS分类;

:保存计取结果;

stop
@enduml
```

### 7.4 决策点设计

#### 7.4.1 识别方式选择
**决策因素**：
- 专业类型的标准化程度
- 表格格式的规范性
- 历史识别成功率
- 用户偏好设置

**决策逻辑**：
- 工艺管道等标准化专业优先使用代码识别
- 复杂格式或非标准表格使用AI识别
- 支持用户手动选择识别方式

#### 7.4.2 匹配策略选择
**完全匹配条件**：
- 所有必要要素完全匹配
- 匹配度达到100%
- 无歧义性冲突

**推荐匹配条件**：
- 匹配度达到70%以上
- 关键要素匹配
- 存在可接受的差异

**人工干预条件**：
- 匹配度低于70%
- 存在多个同等匹配结果
- 用户主动选择人工模式

## 8. 非功能性需求设计

### 8.1 性能设计

#### 8.1.1 性能指标
- **响应时间**：系统平均响应时间 ≤ 5秒
- **并发处理**：支持100个并发用户同时操作
- **文件处理**：单个Excel文件处理时间 ≤ 10分钟（50万字符以内）
- **数据吞吐**：支持单次处理5000万字符的文件

### 8.3 安全设计

#### 8.3.1 认证授权
**认证机制**：
- 集成统一认证系统
- Spring Security & Token管理
- 会话超时控制

**授权控制**：
- 基于角色的访问控制
- 细粒度权限控制
- 数据权限隔离
- API访问控制

本概要设计说明书为智能化概算编制系统的实施提供了全面的技术指导，后续的详细设计和开发实施应严格按照本设计方案执行，确保系统的质量和可维护性。
