# 智能化概算编制系统概要设计说明书

## 1. 引言

### 1.1 编写目的
本文档描述了基于前后端分离架构的智能化概算编制系统的概要设计方案，为系统开发、测试和维护提供技术指导。

### 1.2 项目背景
实现以设计条件为数据源头，通过设计条件的拆分识别，完成概算指标的套取、系数调整的计取、价格套取，输出规定格式报表，将结果数据与编制软件完成数据接口。

### 1.3 系统定位
- **前端**：Vue.js框架，负责用户界面展示、交互逻辑、数据请求
- **后端**：Java Spring Boot，负责业务逻辑处理、数据存储、API提供
- **架构模式**：前后端分离

## 2. 系统架构设计

### 2.1 总体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Vue.js)                        │
├─────────────────────────────────────────────────────────────┤
│  设计条件表导入  │  条件表代码识别  │  条件表大模型识别  │  识别结果调整  │
├─────────────────────────────────────────────────────────────┤
│                        API网关层                             │
├─────────────────────────────────────────────────────────────┤
│                        后端服务层 (Spring Boot)                │
├─────────────────────────────────────────────────────────────┤
│  项目管理模块  │  组价中心模块  │  价格库管理模块  │  系统管理模块  │
├─────────────────────────────────────────────────────────────┤
│                        数据访问层 (MyBatis Plus)              │
├─────────────────────────────────────────────────────────────┤
│                        数据存储层 (MySQL/Redis)               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术架构

#### 2.2.1 前端技术栈
- **框架**：Vue.js 3.x
- **UI组件库**：Element Plus
- **状态管理**：Vuex/Pinia
- **路由管理**：Vue Router
- **HTTP客户端**：Axios
- **构建工具**：Vite/Webpack

#### 2.2.2 后端技术栈
- **框架**：Spring Boot 2.7.18
- **ORM框架**：MyBatis Plus
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **文档工具**：Swagger/OpenAPI
- **安全框架**：Spring Security
- **文件处理**：Apache POI (Excel处理)

### 2.3 模块划分

#### 2.3.1 核心业务模块
1. **项目管理模块** (project-management)
2. **组价中心模块** (pricing-center)
3. **价格库管理模块** (price-library)
4. **系统管理模块** (system-management)

#### 2.3.2 基础支撑模块
1. **文件处理模块** (file-processing)
2. **AI识别模块** (ai-recognition)
3. **数据导入导出模块** (data-import-export)
4. **权限管理模块** (permission-management)

## 3. 详细设计

### 3.1 项目管理模块

#### 3.1.1 功能概述
负责项目的创建、管理、任务分配和项目设置等功能。

#### 3.1.2 主要实体设计

**项目实体 (ProjectDO)**
```java
@TableName("project")
public class ProjectDO {
    private Long id;                    // 项目ID
    private String projectName;         // 项目名称
    private String projectCode;         // 项目编号
    private String description;         // 项目描述
    private Integer status;             // 项目状态
    private Long creatorId;             // 创建人ID
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
    // 项目设置相关字段
    private BigDecimal copperPrice;     // 铜价
    private String nonStandardPricePeriod; // 非标价格期数
    private String anticorrosionProcess;   // 防腐工艺
    private BigDecimal maxTransportDiameter; // 运输最大内径
    private BigDecimal maxTransportHeight;   // 运输最大长高度
    private Long projectPriceLibraryId;     // 项目价格库ID
}
```

#### 3.1.3 API接口设计

**项目管理控制器 (ProjectController)**
```java
@RestController
@RequestMapping("/algocell/project")
public class ProjectController {
    
    @PostMapping("/create")
    public CommonResult<Long> createProject(@Valid @RequestBody ProjectCreateReqVO reqVO);
    
    @PutMapping("/update")
    public CommonResult<Boolean> updateProject(@Valid @RequestBody ProjectUpdateReqVO reqVO);
    
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteProject(@RequestParam("id") Long id);
    
    @GetMapping("/get")
    public CommonResult<ProjectRespVO> getProject(@RequestParam("id") Long id);
    
    @GetMapping("/page")
    public CommonResult<PageResult<ProjectRespVO>> getProjectPage(@Valid ProjectPageReqVO reqVO);
}
```

### 3.2 组价中心模块

#### 3.2.1 功能概述
核心业务模块，负责设计条件表导入、识别、指标套取、系数调整和价格计取。

#### 3.2.2 主要实体设计

**设计条件表实体 (DesignConditionDO)**
```java
@TableName("design_condition")
public class DesignConditionDO {
    private Long id;                    // 主键ID
    private Long projectId;             // 项目ID
    private String specialty;           // 专业类型
    private String fileName;            // 文件名
    private String originalData;        // 原始数据(JSON格式)
    private String recognizedData;      // 识别后数据(JSON格式)
    private Integer recognitionType;    // 识别类型(1:代码识别 2:大模型识别)
    private Integer status;             // 状态
    private LocalDateTime createTime;   // 创建时间
}
```

**指标套取结果实体 (IndexMatchResultDO)**
```java
@TableName("index_match_result")
public class IndexMatchResultDO {
    private Long id;                    // 主键ID
    private Long designConditionId;     // 设计条件ID
    private String indexCode;           // 指标编码
    private String indexName;           // 指标名称
    private String matchedElements;     // 匹配要素(JSON格式)
    private BigDecimal coefficient;     // 系数
    private BigDecimal quantity;        // 工程量
    private String unit;                // 单位
    private Integer matchType;          // 匹配类型(1:完全匹配 2:推荐匹配)
    private BigDecimal matchScore;      // 匹配度分数
}
```

#### 3.2.3 核心服务设计

**设计条件识别服务 (DesignConditionRecognitionService)**
```java
@Service
public class DesignConditionRecognitionService {
    
    /**
     * 代码逻辑识别
     */
    public RecognitionResult codeRecognition(Long designConditionId, String specialty);
    
    /**
     * 大模型识别
     */
    public RecognitionResult aiRecognition(Long designConditionId, String specialty);
    
    /**
     * 识别结果调整
     */
    public Boolean adjustRecognitionResult(Long designConditionId, AdjustmentReqVO reqVO);
}
```

**指标套取服务 (IndexMatchingService)**
```java
@Service
public class IndexMatchingService {
    
    /**
     * 指标套取
     */
    public List<IndexMatchResult> matchIndex(Long designConditionId);
    
    /**
     * 系数换算
     */
    public CoefficientResult calculateCoefficient(Long indexMatchResultId);
    
    /**
     * 自学习优化
     */
    public Boolean optimizeKnowledgeBase(OptimizationReqVO reqVO);
}
```

### 3.3 价格库管理模块

#### 3.3.1 功能概述
管理行业价格库、企业价格库、项目价格库的创建、维护、查询和版本管理。

#### 3.3.2 主要实体设计

**价格库实体 (PriceLibraryDO)**
```java
@TableName("price_library")
public class PriceLibraryDO {
    private Long id;                    // 主键ID
    private String libraryName;         // 价格库名称
    private String libraryCode;         // 价格库编码
    private Integer libraryType;        // 价格库类型(1:行业 2:企业 3:项目)
    private String version;             // 版本号
    private Integer status;             // 状态(1:编辑中 2:已发布)
    private String description;         // 描述
    private Long parentLibraryId;       // 父价格库ID
    private LocalDateTime publishTime;  // 发布时间
}
```

**价格数据实体 (PriceDataDO)**
```java
@TableName("price_data")
public class PriceDataDO {
    private Long id;                    // 主键ID
    private Long libraryId;             // 价格库ID
    private String itemName;            // 项目名称
    private String specification;       // 规格型号
    private String unit;                // 单位
    private BigDecimal price;           // 价格
    private String elements;            // 要素信息(JSON格式)
    private String specialty;           // 专业
    private LocalDateTime effectiveDate; // 生效日期
}
```

### 3.4 数据库设计

#### 3.4.1 核心表结构

**项目表 (project)**
```sql
CREATE TABLE `project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编号',
  `description` text COMMENT '项目描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '项目状态',
  `creator_id` bigint NOT NULL COMMENT '创建人ID',
  `copper_price` decimal(10,2) DEFAULT '50000' COMMENT '铜价',
  `non_standard_price_period` varchar(50) COMMENT '非标价格期数',
  `anticorrosion_process` varchar(100) COMMENT '防腐工艺',
  `max_transport_diameter` decimal(10,2) DEFAULT '3800' COMMENT '运输最大内径',
  `max_transport_height` decimal(10,2) DEFAULT '20000' COMMENT '运输最大长高度',
  `project_price_library_id` bigint COMMENT '项目价格库ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`)
) COMMENT='项目表';
```

**设计条件表 (design_condition)**
```sql
CREATE TABLE `design_condition` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `specialty` varchar(50) NOT NULL COMMENT '专业类型',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `original_data` longtext COMMENT '原始数据',
  `recognized_data` longtext COMMENT '识别后数据',
  `recognition_type` tinyint DEFAULT '1' COMMENT '识别类型',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_specialty` (`specialty`)
) COMMENT='设计条件表';
```

## 4. 接口设计

### 4.1 RESTful API规范

#### 4.1.1 URL设计规范
- 基础路径：`/api/v1/algocell`
- 资源命名：使用名词复数形式
- 操作方式：使用HTTP动词表示操作类型

#### 4.1.2 响应格式规范
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 4.2 核心API接口

#### 4.2.1 设计条件表导入接口
```http
POST /api/v1/algocell/design-conditions/import
Content-Type: multipart/form-data

参数：
- projectId: 项目ID
- specialty: 专业类型
- file: Excel文件
```

#### 4.2.2 条件识别接口
```http
POST /api/v1/algocell/design-conditions/{id}/recognize
Content-Type: application/json

{
  "recognitionType": 1,  // 1:代码识别 2:大模型识别
  "specialty": "工艺管道"
}
```

#### 4.2.3 指标套取接口
```http
POST /api/v1/algocell/index-matching/match
Content-Type: application/json

{
  "designConditionId": 123,
  "specialty": "工艺管道",
  "additionalElements": {}
}
```

## 5. 安全设计

### 5.1 认证授权
- 集成统一认证系统
- 基于JWT的无状态认证
- 细粒度的权限控制

### 5.2 数据安全
- 敏感数据加密存储
- API接口访问频率限制
- 操作日志记录

### 5.3 文件安全
- 文件类型校验
- 文件大小限制
- 病毒扫描

## 6. 性能设计

### 6.1 性能指标
- 系统平均响应时间：≤5秒
- 最大并发响应时间：≤8秒
- 单个专业处理时间：≤10分钟
- 系统资源占用率：≤70%

### 6.2 性能优化策略
- Redis缓存热点数据
- 数据库索引优化
- 异步处理大文件
- 分页查询优化

## 7. 部署架构

### 7.1 部署环境
- **开发环境**：单机部署
- **测试环境**：容器化部署
- **生产环境**：集群部署

### 7.2 技术组件
- **应用服务器**：Spring Boot内嵌Tomcat
- **数据库**：MySQL主从架构
- **缓存**：Redis集群
- **负载均衡**：Nginx
- **容器化**：Docker + Kubernetes

## 8. 监控运维

### 8.1 系统监控
- 应用性能监控(APM)
- 数据库性能监控
- 服务器资源监控

### 8.2 日志管理
- 结构化日志记录
- 日志集中收集
- 日志分析告警

### 4.3 前后端交互流程

#### 4.3.1 设计条件表导入流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant DB as 数据库
    participant FS as 文件存储

    F->>B: 上传Excel文件
    B->>FS: 保存文件
    B->>B: 解析Excel内容
    B->>DB: 保存原始数据
    B->>F: 返回导入结果
```

#### 4.3.2 AI识别处理流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant AI as AI服务
    participant KB as 知识库

    F->>B: 请求AI识别
    B->>KB: 获取知识库数据
    B->>AI: 调用大模型识别
    AI->>B: 返回识别结果
    B->>DB: 保存识别结果
    B->>F: 返回处理状态
```

## 5. 前端设计规范

### 5.1 组件设计规范

#### 5.1.1 页面组件结构
```
src/
├── views/                    # 页面组件
│   ├── project/             # 项目管理页面
│   │   ├── ProjectList.vue  # 项目列表
│   │   ├── ProjectForm.vue  # 项目表单
│   │   └── ProjectDetail.vue # 项目详情
│   ├── pricing/             # 组价中心页面
│   │   ├── ConditionImport.vue    # 条件表导入
│   │   ├── RecognitionResult.vue  # 识别结果
│   │   ├── IndexMatching.vue      # 指标套取
│   │   └── PriceCalculation.vue   # 价格计取
│   └── library/             # 价格库管理页面
├── components/              # 通用组件
│   ├── FileUpload/         # 文件上传组件
│   ├── DataTable/          # 数据表格组件
│   └── FormBuilder/        # 表单构建组件
└── api/                    # API接口
    ├── project.js          # 项目相关API
    ├── pricing.js          # 组价相关API
    └── library.js          # 价格库相关API
```

#### 5.1.2 状态管理设计
```javascript
// store/modules/project.js
export default {
  namespaced: true,
  state: {
    currentProject: null,
    projectList: [],
    loading: false
  },
  mutations: {
    SET_CURRENT_PROJECT(state, project) {
      state.currentProject = project;
    },
    SET_PROJECT_LIST(state, list) {
      state.projectList = list;
    },
    SET_LOADING(state, loading) {
      state.loading = loading;
    }
  },
  actions: {
    async fetchProjectList({ commit }, params) {
      commit('SET_LOADING', true);
      try {
        const response = await projectApi.getProjectPage(params);
        commit('SET_PROJECT_LIST', response.data.list);
        return response;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};
```

### 5.2 前端特定功能实现

#### 5.2.1 设计条件表导入组件
```vue
<template>
  <div class="condition-import">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="beforeUpload"
      :on-success="onUploadSuccess"
      :on-error="onUploadError"
      drag
      accept=".xlsx,.xls"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将Excel文件拖拽到此处，或<em>点击上传</em>
      </div>
    </el-upload>

    <!-- 上传进度 -->
    <el-progress
      v-if="uploading"
      :percentage="uploadProgress"
      :status="progressStatus"
    />

    <!-- 导入结果预览 -->
    <div v-if="importResult" class="import-result">
      <el-table :data="importResult.data" border>
        <el-table-column prop="序号" label="序号" width="80" />
        <el-table-column prop="名称" label="名称" />
        <el-table-column prop="规格" label="规格" />
        <el-table-column prop="单位" label="单位" width="80" />
        <el-table-column prop="工程量" label="工程量" width="100" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';

const store = useStore();
const uploadRef = ref();
const uploading = ref(false);
const uploadProgress = ref(0);
const importResult = ref(null);

const uploadUrl = computed(() => '/api/v1/algocell/design-conditions/import');
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${store.getters.token}`
}));
const uploadData = computed(() => ({
  projectId: store.state.project.currentProject?.id,
  specialty: props.specialty
}));

const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel';
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件！');
    return false;
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB！');
    return false;
  }

  uploading.value = true;
  uploadProgress.value = 0;
  return true;
};

const onUploadSuccess = (response) => {
  uploading.value = false;
  uploadProgress.value = 100;
  importResult.value = response.data;
  ElMessage.success('文件上传成功！');
};

const onUploadError = (error) => {
  uploading.value = false;
  ElMessage.error('文件上传失败：' + error.message);
};
</script>
```

#### 5.2.2 条件表代码识别组件
```vue
<template>
  <div class="code-recognition">
    <el-card header="代码识别配置">
      <el-form :model="recognitionForm" label-width="120px">
        <el-form-item label="专业类型">
          <el-select v-model="recognitionForm.specialty" placeholder="请选择专业">
            <el-option label="工艺管道" value="工艺管道" />
            <el-option label="静置设备" value="静置设备" />
            <el-option label="机械设备" value="机械设备" />
            <el-option label="电气" value="电气" />
            <el-option label="给排水" value="给排水" />
          </el-select>
        </el-form-item>

        <el-form-item label="识别模式">
          <el-radio-group v-model="recognitionForm.mode">
            <el-radio label="auto">自动识别</el-radio>
            <el-radio label="manual">手动配置</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 手动配置选项 -->
        <template v-if="recognitionForm.mode === 'manual'">
          <el-form-item label="列映射配置">
            <column-mapping
              v-model="recognitionForm.columnMapping"
              :columns="originalColumns"
            />
          </el-form-item>
        </template>

        <el-form-item>
          <el-button
            type="primary"
            @click="startRecognition"
            :loading="recognizing"
          >
            开始识别
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 识别结果 -->
    <recognition-result
      v-if="recognitionResult"
      :result="recognitionResult"
      @adjust="onAdjustResult"
    />
  </div>
</template>
```

#### 5.2.3 识别结果调整组件
```vue
<template>
  <div class="result-adjustment">
    <el-card header="识别结果调整">
      <div class="toolbar">
        <el-button @click="addRow">添加行</el-button>
        <el-button @click="deleteSelected">删除选中</el-button>
        <el-button @click="batchEdit">批量编辑</el-button>
        <el-button type="primary" @click="saveAdjustment">保存调整</el-button>
      </div>

      <el-table
        ref="tableRef"
        :data="adjustmentData"
        @selection-change="onSelectionChange"
        border
        height="500"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="序号" label="序号" width="80">
          <template #default="{ row, $index }">
            <el-input
              v-model="row.序号"
              @change="markAsModified(row, $index)"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column prop="名称" label="名称" min-width="200">
          <template #default="{ row, $index }">
            <el-input
              v-model="row.名称"
              @change="markAsModified(row, $index)"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column prop="规格" label="规格" min-width="150">
          <template #default="{ row, $index }">
            <el-input
              v-model="row.规格"
              @change="markAsModified(row, $index)"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column prop="单位" label="单位" width="100">
          <template #default="{ row, $index }">
            <el-select
              v-model="row.单位"
              @change="markAsModified(row, $index)"
              size="small"
            >
              <el-option label="个" value="个" />
              <el-option label="台" value="台" />
              <el-option label="套" value="套" />
              <el-option label="m" value="m" />
              <el-option label="m²" value="m²" />
              <el-option label="m³" value="m³" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="工程量" label="工程量" width="120">
          <template #default="{ row, $index }">
            <el-input-number
              v-model="row.工程量"
              @change="markAsModified(row, $index)"
              size="small"
              :precision="2"
            />
          </template>
        </el-table-column>

        <el-table-column prop="行类型" label="行类型" width="120">
          <template #default="{ row, $index }">
            <el-select
              v-model="row.行类型"
              @change="markAsModified(row, $index)"
              size="small"
            >
              <el-option label="分部" value="分部" />
              <el-option label="子目" value="子目" />
              <el-option label="描述" value="描述" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row, $index }">
            <el-button
              type="danger"
              size="small"
              @click="deleteRow($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const tableRef = ref();
const adjustmentData = ref([]);
const selectedRows = ref([]);
const modifiedRows = new Set();

const markAsModified = (row, index) => {
  modifiedRows.add(index);
  // 标记修改的单元格为红色
  row._modified = true;
};

const addRow = () => {
  adjustmentData.value.push({
    序号: '',
    名称: '',
    规格: '',
    单位: '',
    工程量: 0,
    行类型: '子目',
    _isNew: true
  });
};

const deleteRow = (index) => {
  adjustmentData.value.splice(index, 1);
};

const deleteSelected = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的行');
    return;
  }

  try {
    await ElMessageBox.confirm('确定删除选中的行吗？', '确认删除');
    // 删除选中的行
    const selectedIndices = selectedRows.value.map(row =>
      adjustmentData.value.indexOf(row)
    ).sort((a, b) => b - a);

    selectedIndices.forEach(index => {
      adjustmentData.value.splice(index, 1);
    });

    ElMessage.success('删除成功');
  } catch {
    // 用户取消删除
  }
};

const saveAdjustment = async () => {
  try {
    const response = await pricingApi.saveAdjustment({
      designConditionId: props.designConditionId,
      adjustmentData: adjustmentData.value
    });

    ElMessage.success('保存成功');
    modifiedRows.clear();
  } catch (error) {
    ElMessage.error('保存失败：' + error.message);
  }
};
</script>

<style scoped>
.result-adjustment {
  margin-top: 20px;
}

.toolbar {
  margin-bottom: 15px;
}

/* 修改过的单元格样式 */
:deep(.el-table .el-input__inner) {
  color: red;
}

:deep(.el-table tr[data-modified="true"]) {
  background-color: #fef0f0;
}
</style>
```

## 6. 后端详细设计

### 6.1 包结构设计

```
cn.savas.hub.module.algocell
├── controller/              # 控制器层
│   └── admin/              # 管理端控制器
│       ├── ProjectController.java
│       ├── DesignConditionController.java
│       ├── IndexMatchingController.java
│       └── PriceLibraryController.java
├── service/                # 服务层
│   ├── ProjectService.java
│   ├── DesignConditionService.java
│   ├── RecognitionService.java
│   ├── IndexMatchingService.java
│   └── PriceLibraryService.java
├── dal/                    # 数据访问层
│   ├── dataobject/        # 数据对象
│   └── mysql/             # MySQL映射器
├── convert/               # 对象转换器
├── api/                   # API接口定义
└── framework/             # 框架配置
```

### 6.2 核心服务实现

#### 6.2.1 设计条件识别服务实现
```java
@Service
@Slf4j
public class DesignConditionRecognitionServiceImpl implements DesignConditionRecognitionService {

    @Resource
    private DesignConditionMapper designConditionMapper;

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @Resource
    private AiRecognitionService aiRecognitionService;

    @Override
    @Transactional
    public RecognitionResult codeRecognition(Long designConditionId, String specialty) {
        log.info("开始代码识别，设计条件ID: {}, 专业: {}", designConditionId, specialty);

        // 1. 获取设计条件数据
        DesignConditionDO designCondition = designConditionMapper.selectById(designConditionId);
        if (designCondition == null) {
            throw new ServiceException(ErrorCodeConstants.DESIGN_CONDITION_NOT_EXISTS);
        }

        // 2. 解析原始数据
        List<Map<String, Object>> originalData = parseOriginalData(designCondition.getOriginalData());

        // 3. 根据专业获取识别规则
        RecognitionRules rules = getRecognitionRules(specialty);

        // 4. 执行代码识别
        RecognitionResult result = executeCodeRecognition(originalData, rules);

        // 5. 保存识别结果
        designCondition.setRecognizedData(JSON.toJSONString(result.getData()));
        designCondition.setRecognitionType(RecognitionTypeEnum.CODE_RECOGNITION.getValue());
        designCondition.setStatus(DesignConditionStatusEnum.RECOGNIZED.getValue());
        designConditionMapper.updateById(designCondition);

        log.info("代码识别完成，识别出 {} 条数据", result.getData().size());
        return result;
    }

    @Override
    @Transactional
    public RecognitionResult aiRecognition(Long designConditionId, String specialty) {
        log.info("开始AI识别，设计条件ID: {}, 专业: {}", designConditionId, specialty);

        // 1. 获取设计条件数据
        DesignConditionDO designCondition = designConditionMapper.selectById(designConditionId);
        if (designCondition == null) {
            throw new ServiceException(ErrorCodeConstants.DESIGN_CONDITION_NOT_EXISTS);
        }

        // 2. 获取知识库数据
        KnowledgeBase knowledgeBase = knowledgeBaseService.getBySpecialty(specialty);

        // 3. 调用AI识别服务
        AiRecognitionRequest request = AiRecognitionRequest.builder()
            .originalData(designCondition.getOriginalData())
            .specialty(specialty)
            .knowledgeBase(knowledgeBase)
            .build();

        RecognitionResult result = aiRecognitionService.recognize(request);

        // 4. 保存识别结果
        designCondition.setRecognizedData(JSON.toJSONString(result.getData()));
        designCondition.setRecognitionType(RecognitionTypeEnum.AI_RECOGNITION.getValue());
        designCondition.setStatus(DesignConditionStatusEnum.RECOGNIZED.getValue());
        designConditionMapper.updateById(designCondition);

        log.info("AI识别完成，识别出 {} 条数据", result.getData().size());
        return result;
    }

    /**
     * 执行代码识别逻辑
     */
    private RecognitionResult executeCodeRecognition(List<Map<String, Object>> originalData,
                                                   RecognitionRules rules) {
        List<RecognizedItem> recognizedItems = new ArrayList<>();

        for (Map<String, Object> row : originalData) {
            RecognizedItem item = new RecognizedItem();

            // 识别序号
            item.setSequenceNumber(extractValue(row, rules.getSequenceColumns()));

            // 识别名称
            item.setName(extractValue(row, rules.getNameColumns()));

            // 识别规格
            item.setSpecification(extractValue(row, rules.getSpecificationColumns()));

            // 识别单位
            item.setUnit(extractValue(row, rules.getUnitColumns()));

            // 识别工程量
            item.setQuantity(extractNumericValue(row, rules.getQuantityColumns()));

            // 识别行类型
            item.setRowType(recognizeRowType(item.getSequenceNumber(), item.getName()));

            recognizedItems.add(item);
        }

        return RecognitionResult.builder()
            .data(recognizedItems)
            .totalCount(recognizedItems.size())
            .successCount(recognizedItems.size())
            .build();
    }

    /**
     * 识别行类型
     */
    private String recognizeRowType(String sequenceNumber, String name) {
        // 序号列为大写一、二、三...识别为分部
        if (StringUtils.isNotBlank(sequenceNumber) &&
            sequenceNumber.matches("[一二三四五六七八九十]+")) {
            return "分部";
        }

        // 只有名称，没有其他任何信息识别为描述
        if (StringUtils.isNotBlank(name) && StringUtils.isBlank(sequenceNumber)) {
            return "描述";
        }

        // 其他情况识别为子目
        return "子目";
    }
}
```

#### 6.2.2 指标套取服务实现
```java
@Service
@Slf4j
public class IndexMatchingServiceImpl implements IndexMatchingService {

    @Resource
    private IndexLibraryService indexLibraryService;

    @Resource
    private MatchingEngineService matchingEngineService;

    @Resource
    private IndexMatchResultMapper indexMatchResultMapper;

    @Override
    @Transactional
    public List<IndexMatchResult> matchIndex(Long designConditionId) {
        log.info("开始指标套取，设计条件ID: {}", designConditionId);

        // 1. 获取识别后的设计条件数据
        List<RecognizedItem> recognizedItems = getRecognizedItems(designConditionId);

        // 2. 获取指标库数据
        List<IndexItem> indexItems = indexLibraryService.getAllIndexItems();

        List<IndexMatchResult> matchResults = new ArrayList<>();

        for (RecognizedItem item : recognizedItems) {
            // 3. 提取要素
            ElementSet elements = extractElements(item);

            // 4. 执行匹配
            MatchingResult matchingResult = matchingEngineService.match(elements, indexItems);

            if (matchingResult.isFullMatch()) {
                // 完全匹配
                IndexMatchResult result = createMatchResult(item, matchingResult.getBestMatch(),
                    MatchTypeEnum.FULL_MATCH, BigDecimal.ONE);
                matchResults.add(result);

            } else if (!matchingResult.getRecommendations().isEmpty()) {
                // 推荐匹配
                IndexItem bestRecommendation = matchingResult.getRecommendations().get(0);
                IndexMatchResult result = createMatchResult(item, bestRecommendation,
                    MatchTypeEnum.RECOMMENDATION_MATCH, matchingResult.getMatchScore());
                matchResults.add(result);

            } else {
                // 无匹配
                log.warn("无法匹配指标，项目: {}", item.getName());
            }
        }

        // 5. 保存匹配结果
        batchSaveMatchResults(matchResults);

        log.info("指标套取完成，匹配 {} 条数据", matchResults.size());
        return matchResults;
    }

    @Override
    public CoefficientResult calculateCoefficient(Long indexMatchResultId) {
        log.info("开始系数换算，匹配结果ID: {}", indexMatchResultId);

        // 1. 获取匹配结果
        IndexMatchResultDO matchResult = indexMatchResultMapper.selectById(indexMatchResultId);
        if (matchResult == null) {
            throw new ServiceException(ErrorCodeConstants.INDEX_MATCH_RESULT_NOT_EXISTS);
        }

        // 2. 获取要素信息
        ElementSet elements = JSON.parseObject(matchResult.getMatchedElements(), ElementSet.class);

        // 3. 获取系数换算规则
        List<CoefficientRule> coefficientRules = getCoefficientRules(matchResult.getIndexCode());

        // 4. 执行系数计算
        BigDecimal finalCoefficient = BigDecimal.ONE;
        List<CoefficientDetail> details = new ArrayList<>();

        for (CoefficientRule rule : coefficientRules) {
            if (rule.matches(elements)) {
                BigDecimal coefficient = rule.calculate(elements);
                finalCoefficient = finalCoefficient.multiply(coefficient);

                details.add(CoefficientDetail.builder()
                    .ruleName(rule.getName())
                    .coefficient(coefficient)
                    .description(rule.getDescription())
                    .build());
            }
        }

        // 5. 更新匹配结果
        matchResult.setCoefficient(finalCoefficient);
        indexMatchResultMapper.updateById(matchResult);

        return CoefficientResult.builder()
            .finalCoefficient(finalCoefficient)
            .details(details)
            .build();
    }

    /**
     * 提取要素信息
     */
    private ElementSet extractElements(RecognizedItem item) {
        ElementSet elements = new ElementSet();

        // 基础要素
        elements.put("名称", item.getName());
        elements.put("规格", item.getSpecification());
        elements.put("单位", item.getUnit());
        elements.put("工程量", item.getQuantity());

        // 专业特定要素提取
        extractSpecialtyElements(item, elements);

        return elements;
    }

    /**
     * 专业特定要素提取
     */
    private void extractSpecialtyElements(RecognizedItem item, ElementSet elements) {
        String specialty = item.getSpecialty();

        switch (specialty) {
            case "工艺管道":
                extractPipelineElements(item, elements);
                break;
            case "静置设备":
                extractStaticEquipmentElements(item, elements);
                break;
            case "机械设备":
                extractMechanicalEquipmentElements(item, elements);
                break;
            // 其他专业...
        }
    }

    /**
     * 工艺管道要素提取
     */
    private void extractPipelineElements(RecognizedItem item, ElementSet elements) {
        String specification = item.getSpecification();

        // 提取管径
        Pattern diameterPattern = Pattern.compile("DN(\\d+)");
        Matcher matcher = diameterPattern.matcher(specification);
        if (matcher.find()) {
            elements.put("管径", matcher.group(1));
        }

        // 提取压力等级
        Pattern pressurePattern = Pattern.compile("PN(\\d+)");
        matcher = pressurePattern.matcher(specification);
        if (matcher.find()) {
            elements.put("压力等级", matcher.group(1));
        }

        // 提取材质
        if (specification.contains("不锈钢")) {
            elements.put("材质", "不锈钢");
        } else if (specification.contains("碳钢")) {
            elements.put("材质", "碳钢");
        }

        // 提取温度
        Pattern temperaturePattern = Pattern.compile("(\\d+)℃");
        matcher = temperaturePattern.matcher(specification);
        if (matcher.find()) {
            elements.put("温度", matcher.group(1));
        }
    }
}
```

## 7. 数据库详细设计

### 7.1 完整表结构设计

#### 7.1.1 知识库相关表
```sql
-- 知识库表
CREATE TABLE `knowledge_base` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '知识库名称',
  `specialty` varchar(50) NOT NULL COMMENT '专业类型',
  `version` varchar(20) NOT NULL COMMENT '版本号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:编辑中 2:已发布)',
  `description` text COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_specialty` (`specialty`),
  KEY `idx_status` (`status`)
) COMMENT='知识库表';

-- 标签表
CREATE TABLE `knowledge_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_base_id` bigint NOT NULL COMMENT '知识库ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父标签ID',
  `tag_name` varchar(100) NOT NULL COMMENT '标签名称',
  `tag_level` tinyint NOT NULL COMMENT '标签层级(1:一级 2:二级 3:要素)',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_parent_id` (`parent_id`)
) COMMENT='知识库标签表';

-- 术语表
CREATE TABLE `knowledge_term` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `term_text` varchar(500) NOT NULL COMMENT '术语文本',
  `is_regex` tinyint DEFAULT '0' COMMENT '是否正则表达式',
  `weight` decimal(3,2) DEFAULT '1.00' COMMENT '权重',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_tag_id` (`tag_id`)
) COMMENT='知识库术语表';
```

#### 7.1.2 匹配库相关表
```sql
-- 指标匹配库表
CREATE TABLE `index_match_library` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `index_code` varchar(50) NOT NULL COMMENT '指标编码',
  `index_name` varchar(200) NOT NULL COMMENT '指标名称',
  `specialty` varchar(50) NOT NULL COMMENT '专业类型',
  `unit` varchar(20) COMMENT '单位',
  `base_price` decimal(12,4) COMMENT '基础价格',
  `required_elements` json COMMENT '必要要素(JSON格式)',
  `coefficient_rules` json COMMENT '系数规则(JSON格式)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_index_code` (`index_code`),
  KEY `idx_specialty` (`specialty`)
) COMMENT='指标匹配库表';

-- 价格匹配库表
CREATE TABLE `price_match_library` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `library_id` bigint NOT NULL COMMENT '价格库ID',
  `item_code` varchar(50) COMMENT '项目编码',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `specification` varchar(500) COMMENT '规格型号',
  `unit` varchar(20) COMMENT '单位',
  `price` decimal(12,4) NOT NULL COMMENT '价格',
  `specialty` varchar(50) NOT NULL COMMENT '专业类型',
  `required_elements` json COMMENT '必要要素(JSON格式)',
  `effective_date` date COMMENT '生效日期',
  `expire_date` date COMMENT '失效日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_library_id` (`library_id`),
  KEY `idx_specialty` (`specialty`),
  KEY `idx_item_name` (`item_name`)
) COMMENT='价格匹配库表';
```

#### 7.1.3 业务流程相关表
```sql
-- 指标套取结果表
CREATE TABLE `index_match_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `design_condition_id` bigint NOT NULL COMMENT '设计条件ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `item_sequence` varchar(50) COMMENT '项目序号',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `specification` varchar(500) COMMENT '规格型号',
  `unit` varchar(20) COMMENT '单位',
  `quantity` decimal(12,4) COMMENT '工程量',
  `index_code` varchar(50) COMMENT '匹配的指标编码',
  `index_name` varchar(200) COMMENT '匹配的指标名称',
  `matched_elements` json COMMENT '匹配的要素(JSON格式)',
  `coefficient` decimal(8,4) DEFAULT '1.0000' COMMENT '系数',
  `match_type` tinyint COMMENT '匹配类型(1:完全匹配 2:推荐匹配 3:手动选择)',
  `match_score` decimal(3,2) COMMENT '匹配度分数',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_design_condition_id` (`design_condition_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_index_code` (`index_code`)
) COMMENT='指标套取结果表';

-- 价格计取结果表
CREATE TABLE `price_match_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `index_match_result_id` bigint NOT NULL COMMENT '指标匹配结果ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `library_type` tinyint NOT NULL COMMENT '价格库类型(1:行业 2:企业 3:项目)',
  `library_id` bigint NOT NULL COMMENT '价格库ID',
  `price_item_id` bigint COMMENT '价格项目ID',
  `matched_price` decimal(12,4) COMMENT '匹配的价格',
  `final_price` decimal(12,4) COMMENT '最终价格(含调整)',
  `price_adjustment` json COMMENT '价格调整信息(JSON格式)',
  `match_type` tinyint COMMENT '匹配类型',
  `match_score` decimal(3,2) COMMENT '匹配度分数',
  `is_selected` tinyint DEFAULT '0' COMMENT '是否被选中',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_index_match_result_id` (`index_match_result_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_library_id` (`library_id`)
) COMMENT='价格计取结果表';

-- CBS分类结果表
CREATE TABLE `cbs_classification_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `index_match_result_id` bigint NOT NULL COMMENT '指标匹配结果ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `cbs_level1` varchar(100) COMMENT 'CBS一级分类',
  `cbs_level2` varchar(100) COMMENT 'CBS二级分类',
  `cbs_level3` varchar(100) COMMENT 'CBS三级分类',
  `cbs_code` varchar(50) COMMENT 'CBS编码',
  `description_object` varchar(200) COMMENT '描述对象',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_index_match_result_id` (`index_match_result_id`),
  KEY `idx_project_id` (`project_id`)
) COMMENT='CBS分类结果表';
```

### 7.2 索引设计策略

#### 7.2.1 查询优化索引
```sql
-- 复合索引优化常用查询
CREATE INDEX `idx_project_specialty_status` ON `design_condition` (`project_id`, `specialty`, `status`);
CREATE INDEX `idx_library_specialty_status` ON `price_match_library` (`library_id`, `specialty`, `effective_date`);
CREATE INDEX `idx_project_match_type` ON `index_match_result` (`project_id`, `match_type`, `status`);

-- 全文索引优化搜索
ALTER TABLE `price_match_library` ADD FULLTEXT(`item_name`, `specification`);
ALTER TABLE `index_match_library` ADD FULLTEXT(`index_name`);
```

## 8. 总结

本概要设计说明书基于前后端分离架构，采用Spring Boot + Vue.js技术栈，设计了智能化概算编制系统的整体架构、模块划分、数据库设计和接口规范。系统具备良好的可扩展性、可维护性和安全性，能够满足智能化概算编制的业务需求。

### 8.1 设计特点
1. **前后端分离**：清晰的职责划分，前端专注UI交互，后端专注业务逻辑
2. **模块化设计**：按业务功能划分模块，便于开发和维护
3. **智能识别**：结合代码识别和AI识别，提高识别准确率
4. **灵活匹配**：支持完全匹配和推荐匹配，适应不同场景需求
5. **可扩展架构**：支持多专业、多价格库的扩展需求

### 8.2 技术优势
1. **成熟技术栈**：采用Spring Boot、Vue.js等成熟稳定的技术
2. **高性能设计**：Redis缓存、数据库优化、异步处理等性能优化措施
3. **安全可靠**：完善的权限控制、数据加密、操作日志等安全机制
4. **易于维护**：清晰的代码结构、完善的文档、规范的开发流程
