package cn.savas.hub.module.algocell.dal.dataobject.proxy;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 代理转发日志记录 DO
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@TableName("aicc_log_proxy")
@KeySequence(value = "aicc_proxy_log_seq")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProxyLogDO{

    /**
     * 请求参数最大长度
     */
    public static final Integer REQUEST_PARAMS_MAX_LENGTH = 8000;

    /**
     * 响应结果最大长度
     */
    public static final Integer RESPONSE_BODY_MAX_LENGTH = 8000;

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * JMG编号 - 用于标识和关联
     */
    private String jmgCode;

    // ========== 请求相关字段 ==========

    /**
     * 请求方法名 (GET, POST, PUT, DELETE等)
     */
    private String requestMethod;

    /**
     * 原始请求地址
     */
    private String originalUrl;

    /**
     * 目标转发地址
     */
    private String targetUrl;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 请求头信息
     */
    private String requestHeaders;

    /**
     * 请求体内容
     */
    private String requestBody;

    /**
     * 用户IP
     */
    private String userIp;

    /**
     * 浏览器UA
     */
    private String userAgent;

    // ========== 响应相关字段 ==========

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 响应头信息
     */
    private String responseHeaders;

    /**
     * 响应结果
     */
    private String responseBody;

    // ========== 执行相关字段 ==========

    /**
     * 开始请求时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束请求时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长，单位：毫秒
     */
    private Integer duration;

    /**
     * 是否成功转发
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 备注信息
     */
    private String remark;

}
