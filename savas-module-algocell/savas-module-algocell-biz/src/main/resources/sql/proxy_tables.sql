-- 代理转发日志记录表
CREATE TABLE `aicc_proxy_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `jmg_code` varchar(64) NOT NULL COMMENT 'JMG编号',
  `trace_id` varchar(64) DEFAULT NULL COMMENT '链路追踪编号',
  `user_id` bigint DEFAULT NULL COMMENT '用户编号',
  `user_type` tinyint DEFAULT NULL COMMENT '用户类型',
  `application_name` varchar(50) NOT NULL COMMENT '应用名',
  `request_method` varchar(16) NOT NULL COMMENT '请求方法名',
  `original_url` varchar(500) NOT NULL COMMENT '原始请求地址',
  `target_url` varchar(500) NOT NULL COMMENT '目标转发地址',
  `request_params` text COMMENT '请求参数',
  `request_headers` text COMMENT '请求头信息',
  `request_body` text COMMENT '请求体内容',
  `user_ip` varchar(50) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '浏览器UA',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_headers` text COMMENT '响应头信息',
  `response_body` text COMMENT '响应结果',
  `begin_time` datetime NOT NULL COMMENT '开始请求时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束请求时间',
  `duration` int DEFAULT NULL COMMENT '执行时长(毫秒)',
  `success` bit(1) DEFAULT NULL COMMENT '是否成功转发',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_jmg_code` (`jmg_code`),
  KEY `idx_begin_time` (`begin_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理转发日志记录表';

-- 功能使用次数记录表
CREATE TABLE `aicc_function_usage_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `jmg_code` varchar(64) NOT NULL COMMENT 'JMG编号',
  `function_code` varchar(64) NOT NULL COMMENT '功能代码',
  `function_name` varchar(100) DEFAULT NULL COMMENT '功能名称',
  `user_id` bigint DEFAULT NULL COMMENT '用户编号',
  `user_type` tinyint DEFAULT NULL COMMENT '用户类型',
  `user_ip` varchar(50) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '浏览器UA',
  `usage_time` datetime NOT NULL COMMENT '使用时间',
  `request_params` text COMMENT '请求参数',
  `result` varchar(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '执行结果(SUCCESS/FAILED)',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `proxy_log_id` bigint DEFAULT NULL COMMENT '关联的代理日志ID',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_jmg_code` (`jmg_code`),
  KEY `idx_function_code` (`function_code`),
  KEY `idx_jmg_function` (`jmg_code`, `function_code`),
  KEY `idx_usage_time` (`usage_time`),
  KEY `idx_proxy_log_id` (`proxy_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能使用次数记录表';
