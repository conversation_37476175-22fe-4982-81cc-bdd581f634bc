# **研发需求分析说明书**

# 编写目的

本文描述了以设计为源头的智能概算编制系统的需求内容，给出最终要实现的系统需满足的功能、性能以及设计约束等，为技术方案、测试方案设计提供依据。

本文预期读者，包括：技术管理人员、项目设计人员、项目开发人员及有关人员。

# 任务概述

## 总体目标

内容：实现以设计条件为数据源头，通过设计条件的拆分识别，完成概算指标的套取、系数调整的计取、价格套取。输出规定格式报表，将结果数据与编制软件完成数据接口。

# 需求分析

## 总体结构

功能结构图：

![](media/image3.png){width="6.083333333333333in"
height="4.427083333333333in"}

功能结构表：

3.2 项目管理

3.2.1 新建项目

3.2.2 新建分类

3.2.3 新建单项

3.2.4 新建专业

3.2.5 项目设置

3.3 组价中心

3.3.1 工程量条件表导入

3.3.2 识别结果调整

3.3.3 指标套取

3.3.4 系数调整

3.3.5 价格计取

3.4 结果输出

3.4.1 接口开发

3.4.2 报表输出

3.5 价格库管理

3.5.1 价格库创建

3.5.2 价格库查询

3.6 系统管理

3.6.1 角色管理

3.6.2 用户管理

3.6.3 专业维护

## 项目管理

### 新建项目

已实现

### 新建分类

已实现

### 新建单项

已实现

### 新建分项

已实现

### 新建专业

已实现

### 任务分配

功能描述：为已经创建的结构分配编制人员。

（1）人员分配

支持项目创建人根据结构指定编制人。

可根据系统中用户账号情况进行选择。

手动多选：
在结构列表中，支持使用Shift+点击/Ctrl+点击进行多选，批量选择。

右键批量选择：增加将本行编制人同步至以下所有相同专业功能。

按层级选择：
支持选择一个父任务进行分配，其所有层级的子任务均完成人员分配。

（2）人员调整

支持项目创建人将当前编制人进行整体替换。

1.可重新选择编制人进行任务分配。

2.替换编制人时提示是否需要整体替换。

（3）任务发布

完成人员分配后可进行项目发布，发布后编制人仅对自己的任务可见。

任务发布后仍可对当前编制人进行替换。

### 项目设置

功能描述：

修改项目名称、单项、分项、专业的名称、编号；设置项目所用铜价，选择非标价格期数，选择防腐工艺的代码、到货状态、项目价格库设置。

（1）输入

------ ---------------- ------------------ --------------------------------------
  序号   输入内容         操作方式           说明

  1      铜价             键盘录入或下拉选   默认 5万

  2      非标价格期数     下拉选             默认系统中的非标价格文件

  3      防腐工艺         下拉选             维护的工艺代码

  4      运输最大内径     键盘录入           默认3800mm

  5      运输最大长高度   键盘录入           默认20000mm

  6      项目价格库       下拉选             关联项目价格库
------ ---------------- ------------------ --------------------------------------

（2）输出

铜价信息、非标设备价格期数、防腐工艺代码，组价时根据输出结果进行套指标与取价。

约束条件：以上信息支持随时修改，保证数据的时效性。

## 数据库管理

功能描述：满足行业价格库、企业价格库、项目价格库的管理、维护和查询功能，确保系统能够支持多层级、差异化的价格管理体系。

### 行业价格库管理 

行业价格库作为最基础的价格参考层，通常来源于权威的框架协议或行业发布价。

（1）价格库建立（初始导入）

描述：
系统应支持通过解析标准格式的框架协议电子文件，批量导入初始价格数据，创建包含行业价格库。

（2） 价格数据更新

描述：
系统应支持通过文件导入的方式，对现有行业价格库进行批量更新或追加。

支持 清空旧数据，替换为新数据和 仅更新或追加有变动的数据两种模式。

更新操作需有权限控制，并记录操作日志（操作人、时间、库名称)。

更新前可生成变更对比报告，供用户确认。

(3)价格查询

描述： 用户可方便地查询和检索行业价格库中的数据。

支持按关键词（名称、规格型号）进行模糊搜索。

支持高级组合条件检索（如：名称 = "阀门" AND 规格="DN50" ）。

查询结果以列表形式展示，支持分页、排序和导出。

### 企业价格库管理 

企业价格库是在行业价格库基础上，形成的公司级标准价格库，可全专业通用。

（1）价格信息采集与建立

描述： 系统应提供多种方式帮助企业价格库快速初始化。

方式一：
支持以行业价格库为蓝本，通过系数调整（如：整体上浮/下浮5%）、手动微调等方式快速生成新版本的企业价格库。

方式二（全新导入）：
支持通过文件导入方式创建，功能同行业价格库：价格数据更新

描述： 同行业价格库：数据更新，但操作对象为企业价格库。

（3）价格信息查询

2.  描述： 同行业价格库：价格查询，但查询对象为企业价格库。

### 项目价格库管理 

项目价格库是针对特定项目的定制化价格库，来源于企业价格库或行业价格库，并由项目负责人行最终裁定。

（1） 项目价格库生成

描述： 系统应支持项目负责人快速为新建项目初始化价格库。

支持基于企业价格库或行业价格库进行创建，作为项目价格的基准。

支持项目负责人对初始价格进行批量调整（系数调整）和个别调整（手动修改特定条目单价）。

项目价格库必须与特定项目绑定，不同项目间的价格库相互独立。

所有定制调整操作需被记录，形成价格核定日志。

（2）价格数据更新

描述： 在项目执行过程中，可能需要更新价格。

支持从最新的企业/行业价格库中手动选择条目进行"打补丁"式更新。

支持通过文件导入更新，功能同前。

项目价格库的任何更新都需经过项目负责人审批（或记录其操作），以确保权责清晰。

（3） 价格信息查询

描述：
同行业价格库：价格查询，但查询对象为项目价格库，且查询结果应明确显示该项目定制后的价格。

### 知识库管理 

（1） 知识库更新

描述： 知识库支持用户通过更新，调整来完善。

支持 清空旧数据，替换为新数据和 仅更新或追加有变动的数据两种模式。

更新操作需有权限控制，并记录操作日志（操作人、时间、库名称)。

更新前可生成变更对比报告，供用户确认。

（2） 知识库调整

描述： 知识库支持用户进行增、删、改等操作。

支持增删改等全套操作：

增： 手动添加标签，要素，分词。

删： 删除识别错误或重复的项。

改： 直接编辑任何单元格的内容。

以上修改操作完成后均将单元格个字体标记为红色

（2） 知识库信息查询

描述：
同行业价格库：价格查询，但查询对象为知识库，且查询结果应明确显示该条知识的结构。

### 匹配库管理

（1） 匹配库更新

描述：
提供匹配库更新功能，如果知识库更新涉及到匹配库调整，则需要对配匹配库进行增删改等操作。

支持 清空旧数据，替换为新数据和 仅更新或追加有变动的数据两种模式。

更新操作需有权限控制，并记录操作日志（操作人、时间、库名称)。

更新前可生成变更对比报告，供用户确认。

（2） 匹配库调整

描述： 匹配库支持用户进行增、删、改等操作。

支持增删改等全套操作：

增： 手动添加标签，要素，分词。

删： 删除识别错误或重复的项。

改： 直接编辑任何单元格的内容。

以上修改操作完成后均将单元格个字体标记为红色

（2） 匹配库信息查询

描述：
同行业价格库：价格查询，但查询对象为匹配库，且查询结果应明确显示该条信息的结构。

### 通用功能

（1） 数据库版本管理

描述： 所有数据库的更新不应直接覆盖，应形成历史版本。

每次导入或重大调整应生成新版本号（如：V1.0, V2.0）。

支持查看数据条目的历史版本和修改记录。

（2）权限与访问控制

描述： 不同角色对数据库的操作权限应严格分离。

系统管理员：可管理所有数据库。

企业管理员：可管理行业、企业价格库、知识库。

项目负责人：只能创建、管理和查看自己所辖项目的项目价格库。

普通用户：只能查询已被授权访问的数据库。

（3）数据库发布

描述：价格库、数据库文件在完成维护后，需要将以上数据库进行发布，发布后的数据库才能被选择使用。

发布：行业价格库，企业价格库，项目价格库，知识库，匹配库等数据库需要维护人员通过发布进行更新，发布后只能进行数据查询，不支持修改。

正在被应用的数据库无法取消发布，支持创建副本，生成新版价格库。

## 组价中心

### 设计条件表导入

功能描述：高效、准确地将设计院提供的、格式千差万别的工程量条件表导入到系统中提取表格中的原始文本和数据。

（1）文件导入

描述：
系统应支持用户在各专业节点（如工艺管道、电气、给排水）上传工程量条件表。

支持常见格式：Excel (.xlsx, .xls)
，提供清晰的上传界面，支持拖拽上传和点击选择。

（2）多次导入

描述： 支持对同一专业节点分多次导入文件，以应对设计变更或补充条件。

电气专业用电负荷表需要单独导入。

（3）复制粘贴

支持从Excel中复制粘贴内容至条件表中

### 条件表代码识别

功能描述：将导入的工程量条件表，自动转换为系统可识别、可计算的结构化清单数据。

（1）代码逻辑识别：

预设一套通用模板规则库，可识别常见格式的条件表格。识别"序号"、"名称"、"规格描述"、"单位"、"工程量"等表头，自动匹配行类型：分部、子目、描述。

此方式处理速度快，适用于标准化程度高的表格，例：工艺管道专业。

注：

1.  自动对应列标题（如序号号、名称、单位等），无法自动识别内容通过手动对应

2.  自动识别行类型（分部、子目、描述），可手动更改，支持批量设置；

3.  指定了直接费列或人材机列，该列中有金额，以Excel表中的数值导入；未指定列或者列中没金额，导入后套取该指标定额库价格

4.  Excel中的多列规格型号等部分参数信息导入后合并到名称中

5.  适配问题：xls、xlsx、带内联公式等各种Excel均能进行导入；

6.  列有效信息分基本信息和描述信息，基本信息各专业相同。基本信息包括：序号、位号、（定额号）、名称、规格、材质、单位、工程量、设备单价、主材单价、进口设备单价、进口主材单价、直接费单价、人工费单价、材料费单价、机械费单价、备注

7.  行识别类型包括：分部、子目、描述，识别规则：

    序号列为大写一、二、三\...识别为分部

    只有名称，没有其他任何信息(包括序号)的识别为描述

    其他情况全部识别为子目，子分部不做识别。

（2）识别结果生成

描述：识别引擎最终需根据知识库将工程量条件表，拆分为系统标准化的工程量清单格式。

输出结果根据各专业清单表格式进行分别显示，同时工程量清单格式报表支持更新维护，用户可通过导入的形式更新工程量清单格式表。

各专业识别参数如下：

+----------------+-----------------------------------------------------+
| 静置设备       | 1.关键参数精准提取：自动提取静设                    |
|                | 备的关键参数，包括设备名称、数量、设备类型、设计压  |
|                | 力/温度、介质毒性、材料编号、设备重量、塔盘层数、设 |
|                | 备内径、设备高度、单台质量、保温要求、计量单位等。  |
|                |                                                     |
|                | 2.特殊参数识别：识别静设备的特殊参数，如            |
|                | 内件材料、热处理要求等，确保数据的全面性和准确性。  |
+----------------+-----------------------------------------------------+
| 机械设备       | 1\.                                                 |
|                | 进口类型识别：设计条件备注列中标有【进口】字        |
|                | 样的子目行并自动识别并标记为进口设备，并提取其价格\ |
|                | 2.                                                  |
|                | 增值税处理：设备价格自动除以1.13（扣                |
|                | 除13%增值税）后的价格，进口设备价格不做除税处理。\  |
|                | 提供增值税处理记录，方便用户审核和追溯              |
+----------------+-----------------------------------------------------+
| 工业炉         | 1.  工业炉参数识别：\                               |
|                |                                                     |
|                |    提取工业炉的关键参数，包括设备名称、数量、设备类 |
|                | 型、设计压力/温度、介质特性、材料编号、设备重量、炉 |
|                | 膛尺寸、炉体高度、单台质量、保温要求、计量单位等。\ |
|                |     识别工业炉的特                                  |
|                | 殊参数，如燃烧器类型、耐火材料种类、热效率要求等。  |
|                |                                                     |
|                | 2.  实现工                                          |
|                | 业炉类型的自动判别（如加热炉、裂解炉、焚烧炉等）。  |
+----------------+-----------------------------------------------------+
| 金属储罐       | 1.  金属储罐参数识别：\                             |
|                |     提取金属                                        |
|                | 储罐的关键参数，包括储罐名称、数量、罐体尺寸（直径  |
|                | 、高度、容积等）、单台质量、保温要求、计量单位等。\ |
|                |     识别金属储罐的特殊参数，如防腐涂料类            |
|                | 型、内外防腐材料、罐底板边缘防护材料、保温材料、附  |
|                | 件类型（如呼吸阀、安全阀、浮盘等）、热处理要求等。  |
|                |                                                     |
|                | 2.  实现金属储罐类型的自动                          |
|                | 判别，如球罐、内浮顶罐、外浮顶罐、拱顶罐、卧罐等。  |
+----------------+-----------------------------------------------------+
| 工艺管道       | 1、管道：管道管径、管表号、制造方式、               |
|                | 管道等级、温度、材质的识别、是否镀锌、热处理的识别\ |
|                | 2、                                                 |
|                | 阀门：口径、类别、形式、压力等级、连接形式、阀体材  |
|                | 质、密封材质、阀杆材质、特殊要求及结构、温度的识别\ |
|                | 3、特殊件：口径、压力等级、材质                     |
|                | 等参数识别，具体参数参考《配管特殊件概算条件模板》  |
+----------------+-----------------------------------------------------+
| 电气           | 1.电气条件识别，对设备材料名称、规格型              |
|                | 号、材质、额定电压、额定电流、额定容量、额定功率、  |
|                | 防爆等级、防护等级、计量单位等关键参数的识别抽取。  |
+----------------+-----------------------------------------------------+
| 电信           | 1，电信专业条件智能识别\                            |
|                | 2，电信材料电缆种类、芯数、保护管管径的识别         |
+----------------+-----------------------------------------------------+
| 自控仪表       | 1，仪表专业                                         |
|                | 条件智能识别，对仪表设备特征、公称直径等参数的识别  |
|                |                                                     |
|                | 2，仪表材料电缆种类、芯数、保护管材质、管径的识别   |
+----------------+-----------------------------------------------------+
| 信息工程       | 设备类型识别                                        |
+----------------+-----------------------------------------------------+
| 给排水         | 1.  管道：给排水管                                  |
|                | 道的材质、管径识别(公称直径和外径的自动识别与转换)  |
|                |                                                     |
|                | 2.  阀门：给排水阀门的材质、连接方式识别            |
|                |                                                     |
|                | 3.  卫                                              |
|                | 生器具名称识别，消防设施的名称、型号与公称直径识别  |
|                |                                                     |
|                | 4.  给排水构筑物的形式与规格识别                    |
+----------------+-----------------------------------------------------+
| 采暖通风       | 1、暖通设备的类型、效能识别\                        |
|                | 2、暖通材料的种类、公称直径、重量的识别与转换       |
+----------------+-----------------------------------------------------+
| 热工           | 设备类型识别                                        |
+----------------+-----------------------------------------------------+
| 分析化验       | 设备类型识别                                        |
+----------------+-----------------------------------------------------+
| 催             | 设备类型识别                                        |
| 化剂及化学药剂 |                                                     |
+----------------+-----------------------------------------------------+
| 劳动安全卫生   | 主材类型识别                                        |
+----------------+-----------------------------------------------------+
| 环境工程       | 设备类型识别                                        |
+----------------+-----------------------------------------------------+
| 防腐保温       | 1、隔热：隔热材料材质、保护层材料及厚度的识别       |
+----------------+-----------------------------------------------------+

### 条件表大模型识别

功能描述：将导入的工程量条件表，自动转换为系统可识别、可计算的结构化清单数据。综合运用规则匹配和AI语义分析技术，最大限度减少人工录入工作，并保证数据处理过程的可靠性与灵活性。

（1）智能识别引擎

描述：当代码识别规则对部分格式的设计条件不能完成识别时，调用长城大模型对设计条件内容进行语义分析，通过对表格数据（上下文信息）的识别，利用其强大的自然语言理解和推理能力，识别表头含义、数据结构、备注信息等特殊表述。

大模型返回结构化的JSON数据，指明每个数据的属性（如：\`{"项目特征":
"碳钢管到", "单位": "m³", "数量": "12.5"}\`）。

（2）识别结果生成

描述：描述：识别引擎最终需根据知识库将工程量条件表，拆分为系统标准化的工程量清单格式。

（3）各专业识别要求如下：

+----------------+-----------------------------------------------------+
| 静置设备       | 1.塔等设                                            |
|                | 备识别时，需要对内件信息进行识别，内件的内径信息取  |
|                | 设备行信息。如塔盘需要额外套指标与取价，且单独成行  |
|                |                                                     |
|                | 需要根据内径 13800 来判断指标的套取。               |
|                |                                                     |
|                | 根据内径 13800 长高度：47700 来判断价格的系数调整。 |
|                |                                                     |
|                | 2.热处理要求会在备注列标注，热处理                  |
|                | 信息需要套指标，所以对备注列的热处理，需要单独成行  |
|                |                                                     |
|                | 3.根据设备的设计压力、设计温度和介质毒性，自        |
|                | 动划分容器类别（一类、二类或三类）（明确识别规则）  |
|                |                                                     |
|                | 4                                                   |
|                | .换散热器条件表中，管侧与壳侧分两行描述，且各行材质 |
|                | 不在一列填写，导致输出时需要进行组合保证信息完整。  |
+----------------+-----------------------------------------------------+
| 机械设备       | 1.进口类型识别：设计条件备注列中标有【进口】字      |
|                | 样的子目行并自动识别并标记为进口设备，并提取其价格\ |
|                | 2.增值税处理：设备价格自动除以1.13（扣              |
|                | 除13%增值税）后的价格，进口设备价格不做除税处理。\  |
|                | 提供增值税处理记录，方便用户审核和追溯              |
+----------------+-----------------------------------------------------+
| 工艺管道       | 支吊架，管道支吊架需                                |
|                | 要根据材质进行汇总工程量，作为管架除锈防腐的工程量  |
+----------------+-----------------------------------------------------+
| 电气           | 桥架的单位在设计室提交                              |
|                | 的时候可能是【平方米】与指标的单位【吨】不一致，在  |
|                | 不一致的情况下需要根据换算对应关系表计算桥架工程量  |
+----------------+-----------------------------------------------------+
| 自控仪表       | 电缆桥架工程量单位不为【吨                          |
|                | 】时，根据转换表，将桥架工程量转换为以【吨】为单位  |
+----------------+-----------------------------------------------------+
| 给排水         | 根据管道管沟土方的自动汇总计算                      |
+----------------+-----------------------------------------------------+
| 采暖通风       | 暖通材料的种类、公称直径、重量的识别与转换          |
+----------------+-----------------------------------------------------+

### 识别结果调整

功能描述：对工程量条件表的识别结果，根据清单表格式做条件归类显示，识别结果支持用户增、删、改等调整。

（1） 可视化调整界面

描述： 提供一个独立的、专注于数据校验和编辑的页面，清晰展示识别结果。

以表格形式展示识别出的清单数据，允许水平/垂直滚动。

支持增删改等全套操作：

增： 手动添加内容。

删： 删除识别错误或重复的项。

改： 直接编辑任何单元格的内容（项目特征、单位、数量等）。

以上修改操作完成后均将单元格个字体标记为红色

（2）调整确认

描述： 用户调整完毕后，将最终结果供后续流程环节使用。

### 指标套取与系数换算

功能描述：本模块是通过将项目设计条件与指标库中的规则进行匹配，自动套取出符合规则的指标，并完成必要的系数换算。该系统具备AI能力，能够在匹配不充分时通过模型推断和人工审核反馈来不断自我优化，提高未来匹配的准确率。

![](media/image4.png){width="6.2652777777777775in"
height="2.395138888888889in"}

#### 组价信息设置

描述：
工艺管道专业：套指标时部分必要信息不在设计条件中体现，需要增加设置窗口来对信息做补充。

（1）输入

------ -------------- ------------- ------------------------------------------------------------------
  序号   输入内容       操作方式      说明

  1      所属装置类型   下拉选        化工，炼油，干粉煤，水煤浆，LNG

  2      管道安装位置   下拉选        其他，全场工艺，供热外管，空分装置，罐区，泵房，热力站，软化水站

  3      空白输入框     键盘录入      支持用户补充多条要素，不同要素以【，】逗号分隔
------ -------------- ------------- ------------------------------------------------------------------

（2）输出

所属装置类型，管道安装位置，空白输入框，以上内容在进行指标匹配时均作为要素参与模型匹配。

#### 指标套取

描述：
系统接收输入的设计条件识别结果，与指标库中的每条指标所要求的必要要素集进行匹配，完全匹配完成指标套取。

1.  确定对象：将工程量条件表识别的结果输入至匹配流程中，通过筛选出的要素确认描述对象，进而确定描述对象是否缺少必要的要素。

2.  长城大模型匹配：在原始设计条件中，通过长城大模型对缺失的必要要素进行查找，完善设计条件识别要素，将完善结果补充至要素集中。

3.  模型匹配：将要素集输入至指标匹配模型中，通过模型匹配，输出匹配到的概算指标。

4.  完全匹配： 如果输入的设计条件 完全包含
    某条指标要求的全部必要要素，则自动套取该指标，如果同时存在的完全匹配在二个或以上，则默认以要素最多的进行匹配。

5.  未完全匹配：当设计条件与任何指标的必要要素都未完全匹配时，系统应启动智能推荐流程。

排序推荐： 如果模型补充后仍无法完全匹配，系统应根据
"匹配度"（匹配上的要素数量/总必要要素数量）对所有指标进行降序排序。

界面展示： 向用户展示Top
10个最接近的指标推荐列表，并清晰标注每条推荐的匹配度、点击指标后显示已匹配的要素和缺失的要素。

人工选择： 用户可根据专业判断，从推荐列表中选择最合适的指标进行套用

6.  记录匹配路径： 任何成功匹配都应记录所使用的指标ID和匹配到的要素。

7.  各专业匹配情况

    **静置设备专业**

```{=html}
<!-- -->
```
(1) 到货状态的识别

识别规则：直径大于或等于3.8米的塔、容器为分片组对安装；直径小于3.8米，且高度大于或等于20米的塔、容器为分段组对安装；直径小于3.8米，且高度小于20米的塔、容器
为整体安装。

需求描述：需根据项目设置中填写的运输最大内径以及长高度，来调整识别规则，通过设计条件给出的内径、长高度信息，判断到货状态。

注：异径塔（有两个内径信息）取最大值来进行到货状态判断。

![](media/image5.png){width="6.7625in" height="0.9666666666666667in"}

(2) 一行设计条件套出多行指标

空冷器设计条件中会对管束、风机、构架等信息做描述，套指标时这些信息都需要套指标故需要单独成行。

![](media/image6.png){width="6.2625in" height="1.8173611111111112in"}

**工艺管道专业**

(1) 防腐代码识别（是否其他专业有同样要求）

需求描述：以项目设置中预设的防腐工艺作为识别逻辑，通过识别防腐代码套用防腐工艺中的刷漆指标，同时汇总刷漆指标中【底漆】指标的工程量，套取除锈指标。

注：防腐工艺的不同代表相同的防腐代码所实施的指标不同，需要配置不同的套取规则。

2.  支吊架除锈计算

    根据标准管架及材质汇总生成管架除锈防腐工程量，例：镀锌的支吊架进行汇总，材质为不锈钢的则不进行汇总

    **电气专业**

```{=html}
<!-- -->
```
1.  需求描述：电缆、灯具等内容识别后自动复制成两行，指标信息匹配至第一行.

2.  用电负荷表

    通过导入的用电负荷表，识别表中关键参数，根据设计条件套出指标后，合并同指标及工程量，删除这些设计条件。

    注：本功能手工操作比较繁琐，因为数据行上百条，套用指标规则较多，工程量大。涉及各专业电动设备的接线检查。判断条件：加热器\>变频调速\>电压等级\>容量

    **给排水专业**

```{=html}
<!-- -->
```
1.  管沟土方计算：根据【管沟土方工程量参考表】，【非金属管道基础垫层工程量参考表】计算给排水专业中管沟土方的量。

    **防腐保温专业**

```{=html}
<!-- -->
```
(1) 防腐代码识别

需求描述：以项目设置中预设的防腐工艺作为识别逻辑，通过识别防腐代码套用防腐工艺中的刷漆指标，同时汇总刷漆指标中【底漆】指标的工程量，套取除锈指标。

注：防腐工艺的不同代表相同的防腐代码所实施的指标不同，需要配置不同的套取规则

#### 系数换算

描述：
完全匹配的指标，系统需根据设计条件识别要素与指标所包含的换算要素进行匹配，计取相应的换算信息。

1.  模型匹配：将要素集输入至换算匹配模型中，通过模型匹配，输出匹配到的系数换算。

2.  指标匹配：则通过输出的系数换算与指标对应的换算范围最对比，取出交集，得到指标所作的系数调整。

记录匹配路径： 任何成功匹配都应记录所使用的换算ID、名称和匹配到的要素。

#### 自学习与知识库优化 

描述：
系统应将模型补充要素和人工选择的行为记录下来，形成一个用于优化指标库的反馈循环。

日志记录：
所有由模型发起的"要素补充"建议，必须被记录到待审核日志库中。记录内容应包括：原始设计条件、模型补充的要素、推荐的指标、用户最终选择的指标。

管理员审核：
系统管理员有权查看日志库，并审核模型的建议。审核后，可以执行以下操作：

采纳建议：
确认模型补充的要素是正确的，并更新相应指标的必要要素集（即加入这个新要素），从而实现知识库的扩展和优化。

删除建议： 确认模型补充的要素是错误的，不纳入相应指标的必要要素集。

### 价格计取

功能描述：项目设计条件与行业/企业/项目三级价格库中的条目进行智能匹配，自动获取准确的价格。系统遵循"设计条件价
\> 项目价 \> 企业价 \>
行业价"的优先级原则，并在匹配不完美时，通过机器学习模型推荐和人工审核反馈机制来提升匹配成功率与准确性。

约束条件：本行设计条件中已含有价格默认不执行智能取价。

![](media/image7.png){width="6.7555555555555555in" height="2.09375in"}

#### 价格匹配规则修改 {#价格匹配规则修改 .MM-Topic-4}

描述：项目价格库、企业价格库、行业价格库按专业同时搜索，每个价格库取出的价格均列出。

1.  设计条件价格：首先检查设计条件本身是否直接带有价格。如有，则直接采用，不再进行后续搜索。

2.  根据当前专业进行价格匹配，在对应专业的价格库中进行搜索。

3.  行业价格库，企业价格库，项目价格库同时进行价格匹配，匹配到的结果均列出。

取价步距原则：在一个价格库中未能匹配到价格，结合专业，根据【取价步距参考要素表】，在价格库中根据此要素向上取价。例：阀门参考要素：管径，设计条件中管径为DN50，价格库中未匹配时向上取DN55,DN60，直到匹配完成。

取出的价格按照：设计条件价 \> 项目价 \> 企业价 \> 行业价
的优先级顺序进行排序。

#### 价格匹配 {#价格匹配 .MM-Topic-4}

描述：在某一价格库内进行搜索时，系统将设计条件与库中每条价格的必要要素集（如：名称、规格型号、技术参数等）进行匹配。

1.  确定对象：将工程量条件表识别的结果输入至匹配流程中，通过筛选出的要素确认描述对象，进而确定描述对象是否缺少必要的要素。

2.  长城大模型匹配：在原始设计条件中，通过长城大模型对缺失的必要要素进行查找，完善设计条件识别要素，将完善结果补充至要素集中。

3.  模型匹配：将要素集输入至价格匹配模型中，通过模型匹配，输出匹配到的概算指标。

4.  完全匹配：
    如果设计条件完全包含某条价格所要求的全部关键要素，则成功匹配并返回该价格，如果同时存在的完全匹配在二个或以上，则默认以要素最多的进行匹配。

5.  未完全匹配：当设计条件与任何指标的必要要素都未完全匹配时，系统应启动智能推荐流程。

排序推荐： 如果模型补充后仍无法完全匹配，系统应根据
"匹配度"（匹配上的要素数量/总必要要素数量）对所有价格进行降序排序。

界面展示： 向用户展示Top
10个最接近的价格推荐列表，并清晰标注每条推荐的匹配度。

人工选择： 用户可根据专业判断，从推荐列表中选择最合适的价格进行套用

6.  记录匹配路径： 任何成功匹配都应记录所使用的价格ID和匹配到的要素。

7.  各专业匹配情况

    **静置设备专业**

需求描述：非标设备有按到货状态的系数调整、按单重区间进行的系数调整和加值调整的需求。其中，按到货状态的取价是获取套指标中内径和长高度界定的。

![](media/image8.png){width="6.258333333333334in"
height="2.0840277777777776in"}

**机械设备**

需求描述：机械设备中进口的设备较多，出表格时进口与非进口设备在一个sheet中，根据备注列的【进口】标注进行区分，进口设备取价后需要将价格放到【进口价格】列。

**工艺管道**

当阀门取价未取到唯一价格时，跟据推荐价格计算阀门平均价，作为唯一价格套取。（设计条件可能缺少阀杆，阀体等具体材质，导致无法取出唯一价格，此时根据推荐价格进行平均价计算）

**电气专业**

需求描述：电缆、灯具等内容识别后会自动复制成两行，价格信息匹配至第二行。

电缆取价中需要根据
阻燃级别、防白蚁等加价条件进行价格调整，取价数据=电缆价格+加价价格。

#### 知识库优化 {#知识库优化 .MM-Topic-4}

描述：系统应将智能推荐和人工决策的过程转化为学习样本，用于优化未来匹配。

日志记录：所有由模型发起的"要素补充"建议和用户的最终选择，必须被记录到待审核日志库中。

管理员审核：管理员定期审核日志，可执行：

采纳建议：确认模型补充要素下分词正确，并更新相应价格条目的关键要素集，使其更易于被匹配。

## CBS分类

需求：识别指标后根据匹配结果，完成CBS分类。

通过确定的描述对象与CBS关系表进行匹配，完成CBS分类。

## 结果输出

功能描述：本模块是数据流程的最终环节，负责将系统内部处理完成后的匹配结果数据，以两种形式进行输出：

文件形式： 生成符合特定标准的Excel文件及其过程文件。

接口形式：
通过编程接口将结果数据无缝对接到下游的造价编制软件中，实现业务流程自动化，减少人工操作错误和提高效率。

### Excel输出

（1）生成标准结果文件

描述：
系统应能按照预先定义的模板的格式要求，将匹配结果生成一个或多个Excel文件（.xlsx格式）。

输出文件的字段顺序、数据类型、工作表名称必须严格符合AICC系统的导入规范。

支持用户选择输出范围（如：按项目、按专业、按匹配状态）。

支持在导出前预览数据概览。

（2）生成过程文件

描述：
为确保结果的可追溯性，系统在输出最终结果文件时，应同时生成一份或多份过程文件。

过程文件内容应包括：

匹配摘要报告：
统计总条目数、成功匹配数、手动调整数、未匹配数等关键指标。

异常或未匹配项清单：
单独列出所有未成功匹配或需要人工复核的条目及其原因。

（3） 文件管理与下载

描述： 系统应对生成的文件进行管理，并提供下载功能。

导出的文件应保存在服务器指定目录，并与对应的项目或任务关联。

系统界面应提供历史导出文件的列表，支持按时间、项目名称等条件查询和重新下载。

### 接口输出

（1） 提供数据接口

描述： 系统需提供标准的接口，允许编制软件通过编程方式获取匹配结果数据。

数据格式： 请求和响应数据格式使用JSON。

（2）触发方式

被动查询： 编制软件在需要时，调用本系统提供的API来调取数据。

数据内容：
接口传输的数据内容应与Excel输出文件的内容一致，以JSON格式传递。

## 系统管理

### 组织管理、登录

对接统一认证系统。

## 通用功能

### 保存

功能描述：保存修改结果到本地或服务端。离开当前专业，则启动是否保存。

### 备份恢复

功能描述：每次保存则备份编制数据和相应时间。本次保存文件和上次保存文件同时存在。各专业数据都作为独自备份。可按某个专业单独恢复。

### 撤销重做

功能描述：在编制界面，进行增、删、改等操作时支持用户撤销重做，可多次撤销。

### 查找

功能描述：通过关键字查找定位到当前窗口的位置，并可查找下一个。

## 非功能需求

### 用户UI

功能描述：

1.设计风格统一，界面整洁一目了然，排序规整，符合中国石油化工集团有限公司信息和数字化管理部提出的要求。

2.适应不同分辨率、不同尺寸的显示器，保持界面、功能按钮不变形

### 性能需求

功能描述：

1.正常功能操作流程无卡顿，秒级响应

2.上传提交：上传文件大小满足单次单个文件处理量不小于5000万字符

3.性能效率：系统平均响应时间在5秒内；系统在最大并发下，响应时间在8秒内；处理文件不大于50万字符的情况下，单个专业的设计条件表处理时间不大于10分钟。高峰值资源占用率不能超过70-80%，一般情况为40%
