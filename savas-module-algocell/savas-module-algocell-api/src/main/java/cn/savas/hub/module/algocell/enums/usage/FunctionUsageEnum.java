package cn.savas.hub.module.algocell.enums.usage;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/24 14:06
 */
@Getter
public enum FunctionUsageEnum {
    /**
     * 实体锁用户登录
     */
    ENTITY_LOCK_LOGIN("ENTITY_LOCK_LOGIN", "实体锁用户登录"),
    /**
     * 虚拟锁用户登录
     */
    VIRTUAL_LOCK_LOGIN("VIRTUAL_LOCK_LOGIN", "虚拟锁用户登录"),
    ;
    private final String code;
    private final String name;

    FunctionUsageEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FunctionUsageEnum getByCode(String code) {
        for (FunctionUsageEnum value : FunctionUsageEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        FunctionUsageEnum usageEnum = getByCode(code);
        return usageEnum != null ? usageEnum.getName() : null;
    }
}
