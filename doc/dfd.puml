@startuml

title 协同版服务端序列图

actor 客户端
participant "ClientProjectController" as ProjectCtrl
participant "ClientProjectService" as ProjectSvc
participant "ClientFlowController" as FlowCtrl
participant "ClientFlowService" as FlowSvc
participant "ClientConfigController" as ConfigCtrl
participant "ClientConfigService" as ConfigSvc
participant "ClientSystemController" as SystemCtrl
participant "ClientSystemService" as SystemSvc
database "项目数据库" as DB
database "文件存储minio" as FileStore

== 新增项目数据 ==
autonumber
客户端 -> ProjectCtrl : uploadHostModelTableData(createReqVO)
ProjectCtrl -> ProjectSvc : uploadHostModelTableData(createReqVO)
ProjectSvc -> DB : 新增项目元数据
ProjectCtrl <-- ProjectSvc : 返回成功
客户端 <-- ProjectCtrl : 返回成功


== 上传项目文件 ==
autonumber
客户端 -> ProjectCtrl : uploadProjectFile(ProjectID, CompiledVersion, file)
ProjectCtrl -> ProjectSvc : uploadProjectFile(ProjectID, fileName, fileBytes, CompiledVersion)
ProjectSvc -> FileStore : 保存sqlite文件
ProjectSvc -> DB : 更新项目文件记录
ProjectCtrl <-- ProjectSvc : 返回成功
客户端 <-- ProjectCtrl : 返回成功

== 获取工程结构与流程 ==
autonumber
客户端 -> ProjectCtrl : getEngineering(ProjectID)
ProjectCtrl -> ProjectSvc : getEngineeringTree(ProjectID)
ProjectSvc -> DB : 查询工程结构树
ProjectCtrl <-- ProjectSvc : 返回工程结构
客户端 <-- ProjectCtrl : 工程结构

客户端 -> FlowCtrl : getFlowData(originalid)
FlowCtrl -> FlowSvc : getFlowData(originalid)
FlowSvc -> DB : 查询流程节点
FlowCtrl <-- FlowSvc : 返回流程节点
客户端 <-- FlowCtrl : 流程节点

== 操作流程并保存 ==
autonumber
客户端 -> FlowCtrl : saveFlowData(saveFlowDataReqVO)
loop 如果工程有锁则需要重新获取工程结构与流程
FlowCtrl -> FlowSvc : saveFlowData(saveFlowDataReqVO)
    ProjectCtrl -> ProjectSvc : getEngineeringTree(ProjectID)
    ProjectSvc -> DB : 查询工程结构树
    ProjectCtrl <-- ProjectSvc : 返回工程结构
    客户端 <-- ProjectCtrl : 工程结构
end
FlowSvc -> DB : 更新流程节点数据
FlowCtrl <-- FlowSvc : 返回成功
客户端 <-- FlowCtrl : 返回成功


== 合并个人工程到汇总文件 ==
autonumber
客户端 -> ProjectCtrl : mergeProjectFile(req)
ProjectCtrl -> ProjectSvc : mergeProjectFile(req)
ProjectSvc -> FileStore : 读取个人sqlite文件
ProjectSvc -> FileStore : 读取汇总sqlite文件
ProjectSvc -> DB : 合并工程数据
ProjectSvc -> FileStore : 写回汇总sqlite文件
ProjectCtrl <-- ProjectSvc : 返回成功
客户端 <-- ProjectCtrl : 返回成功

== 汇总后重新计算 ==
autonumber
客户端 -> ProjectCtrl : calculateProjectData(req)
ProjectCtrl -> ProjectSvc : calculateProjectData(req)
DB -> ProjectSvc : 获取汇总sqlite文件
ProjectSvc -> DB : 重新计算项目数据
ProjectSvc -> FileStore : 更新汇总sqlite文件
ProjectCtrl <-- ProjectSvc : 返回成功
客户端 <-- ProjectCtrl : 返回成功

== 判断项目文件版本与更新 ==
autonumber
客户端 -> ProjectCtrl : getProjectFileVersion(ProjectID)
ProjectCtrl -> ProjectSvc : getProjectFileVersion(ProjectID)
ProjectSvc -> DB : 查询项目文件版本
ProjectCtrl <-- ProjectSvc : 返回版本号
客户端 <-- ProjectCtrl : 版本号

alt 需要更新
    客户端 -> ProjectCtrl : getProjectFile(ProjectID)
    ProjectCtrl -> ProjectSvc : getProjectFile(ProjectID)
    ProjectSvc -> FileStore : 获取最新个人sqlite文件
    ProjectCtrl <-- ProjectSvc : 返回文件信息
    客户端 <-- ProjectCtrl : 下载文件
end

== 配置管理 ==
autonumber
客户端 -> ConfigCtrl : saveProjectSetting(createReqVO)
ConfigCtrl -> ConfigSvc : saveProjectSetting(createReqVO)
ConfigSvc -> DB : 保存项目配置
ConfigCtrl <-- ConfigSvc : 返回成功
客户端 <-- ConfigCtrl : 返回成功

客户端 -> ConfigCtrl : getProjectSetting(ProjectID)
ConfigCtrl -> ConfigSvc : getProjectSettingActive(ProjectID, type)
ConfigSvc -> DB : 查询项目配置
ConfigCtrl <-- ConfigSvc : 返回配置
客户端 <-- ConfigCtrl : 配置信息

== 系统基础功能 ==
autonumber
客户端 -> SystemCtrl : getUsersByRole(RoleId)
SystemCtrl -> SystemSvc : getUsersByRole(RoleId)
SystemSvc -> DB : 查询用户角色
SystemCtrl <-- SystemSvc : 返回用户列表
客户端 <-- SystemCtrl : 用户列表

客户端 -> SystemCtrl : sendNotice(notice)
SystemCtrl -> SystemSvc : sendNotice(notice)
SystemSvc -> DB : 保存通知
SystemCtrl <-- SystemSvc : 返回成功
客户端 <-- SystemCtrl : 返回成功

@enduml
